[project]
name = "knowlan"
authors = [
    { name = "Frappe Technologies", email = "<EMAIL>"}
]
description = "Visual knowledge base system for Frappe/ERPNext instances"
requires-python = ">=3.10"
readme = "README.md"
dynamic = ["version"]
dependencies = [
    # "frappe~=15.0.0" # Installed and managed by bench.
]

[project.optional-dependencies]
advanced = [
    "networkx>=2.8",
    "leidenalg>=0.9.0",
    "python-igraph>=0.10.0",
    "psutil>=5.9.0",
]

[build-system]
requires = ["flit_core >=3.4,<4"]
build-backend = "flit_core.buildapi"

[tool.ruff]
line-length = 110
target-version = "py310"

[tool.ruff.lint]
select = [
    "F",
    "E",
    "W",
    "I",
    "UP",
    "B",
    "RUF",
]
ignore = [
    "B018", # "useless" expression (used for soft assertions)
    "B019", # Use of `functools.lru_cache` on methods can lead to memory leaks
    "B904", # Within an except clause, raise exceptions with ...
    "E101", # Indentation contains mixed spaces and tabs
    "E111", # Indentation is not a multiple of four
    "E114", # Indentation is not a multiple of four (comment)
    "E117", # Over-indented
    "E501", # Line too long
    "F401", # Module imported but unused
    "F403", # 'from module import *' used; unable to detect undefined names
    "F405", # Name may be undefined, or defined from star imports
    "F722", # Forward annotation syntax error
    "F811", # Redefinition of unused variable
    "I001", # Import block is un-sorted or un-formatted
    "RUF001", # String contains ambiguous unicode character
    "RUF002", # Docstring contains ambiguous unicode character
    "RUF003", # Comment contains ambiguous unicode character
    "UP032", # Use f-string instead of `format` call (translations)
]
typing-modules = ["frappe.types.DF"]

[tool.ruff.format]
quote-style = "double"
indent-style = "tab"
docstring-code-format = true
