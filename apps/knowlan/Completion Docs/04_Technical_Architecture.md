# Technical Architecture and Implementation

## System Architecture Overview

Knowlan follows a modern, modular architecture designed for scalability and maintainability within the Frappe ecosystem.

### Core Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
├─────────────────────────────────────────────────────────────┤
│  System Atlas  │  Learning     │  Insights    │  Audit      │
│  (Cytoscape.js)│  Classroom    │  Dashboard   │  Dashboard  │
│                │  (Monaco)     │  (D3.js)     │  (vis.js)   │
├─────────────────────────────────────────────────────────────┤
│                    API Layer                                │
├─────────────────────────────────────────────────────────────┤
│  Metadata API  │  Graph API    │  Performance │  Developer  │
│  (v1/metadata) │  (v1/graph)   │  API         │  Tools API  │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                            │
├─────────────────────────────────────────────────────────────┤
│  Cache Manager │  Query        │  Graph       │  Performance│
│                │  Optimizer    │  Processor   │  Monitor    │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer                               │
├─────────────────────────────────────────────────────────────┤
│           Frappe Framework & Database                       │
└─────────────────────────────────────────────────────────────┘
```

## Frontend Architecture

### 1. **System Atlas** (`system_atlas.js`)
- **Technology**: Cytoscape.js for graph visualization
- **Features**: Interactive node/edge manipulation, multiple layouts, clustering
- **Performance**: Lazy loading, virtual scrolling for large datasets
- **Styling**: CSS-based theming with dark/light mode support

### 2. **Learning Classroom** (`learning_classroom.js`)
- **Technology**: Monaco Editor for code viewing
- **Features**: Hierarchical navigation, field exploration, note-taking
- **Storage**: LocalStorage for personal notes and preferences
- **Search**: Real-time filtering and search across DocTypes

### 3. **Insights Dashboard** (`insights_dashboard.js`)
- **Technology**: D3.js for custom visualizations
- **Features**: Performance metrics, usage analytics, trend analysis
- **Real-time**: WebSocket integration for live updates
- **Export**: PNG/SVG export capabilities

### 4. **Audit Dashboard** (`audit_dashboard.js`)
- **Technology**: vis.js Timeline for temporal visualizations
- **Features**: Document lifecycle tracking, activity heatmaps
- **Analytics**: Pattern recognition in user behavior
- **Filtering**: Advanced filtering by date, user, DocType

## Backend Architecture

### 1. **API Layer** (`api/v1/`)

#### Metadata API (`metadata.py`)
```python
@frappe.whitelist()
def get_doctypes(include_custom=False, include_child=False, **kwargs):
    """Get DocType metadata with intelligent caching"""
    
    cache_key = f"knowlan_doctypes_{hash(str(kwargs))}"
    cached_data = frappe.cache().get_value(cache_key)
    
    if cached_data:
        return cached_data
    
    data = _generate_doctypes_data(**kwargs)
    frappe.cache().set_value(cache_key, data, expires_in_sec=3600)
    
    return data
```

#### Graph API (`graph.py`)
```python
@frappe.whitelist()
def get_graph_data(layout="spring", clustering=True, **filters):
    """Generate graph data with optional clustering"""
    
    # Get base metadata
    metadata = get_doctypes(**filters)
    
    # Build graph structure
    graph = build_graph_from_metadata(metadata)
    
    # Apply clustering if requested
    if clustering:
        graph = apply_clustering(graph, algorithm="leiden")
    
    # Calculate layout positions
    positions = calculate_layout(graph, layout_type=layout)
    
    return {
        "nodes": graph.nodes(data=True),
        "edges": graph.edges(data=True),
        "positions": positions,
        "statistics": get_graph_statistics(graph)
    }
```

### 2. **Service Layer**

#### Cache Manager (`utils/cache_manager.py`)
```python
class CacheManager:
    """Intelligent caching with TTL and invalidation"""
    
    def __init__(self):
        self.default_ttl = 3600  # 1 hour
        self.cache_keys = set()
    
    def get_or_set(self, key, generator_func, ttl=None):
        """Get from cache or generate and cache"""
        cached = frappe.cache().get_value(key)
        if cached is not None:
            return cached
        
        # Generate fresh data
        data = generator_func()
        
        # Cache with TTL
        ttl = ttl or self.default_ttl
        frappe.cache().set_value(key, data, expires_in_sec=ttl)
        self.cache_keys.add(key)
        
        return data
    
    def invalidate_pattern(self, pattern):
        """Invalidate all keys matching pattern"""
        for key in list(self.cache_keys):
            if pattern in key:
                frappe.cache().delete_value(key)
                self.cache_keys.discard(key)
```

#### Query Optimizer (`utils/query_optimizer.py`)
```python
class QueryOptimizer:
    """Optimize database queries for large datasets"""
    
    @staticmethod
    def optimize_doctype_query(filters, fields, limit=None):
        """Optimize DocType queries with intelligent indexing"""
        
        # Use database-specific optimizations
        if frappe.db.db_type == "mariadb":
            return QueryOptimizer._optimize_mysql_query(filters, fields, limit)
        elif frappe.db.db_type == "postgres":
            return QueryOptimizer._optimize_postgres_query(filters, fields, limit)
        
        # Fallback to standard query
        return frappe.get_all("DocType", filters=filters, fields=fields, limit=limit)
```

## Configuration System

### Site-Based Configuration
```python
# Configuration stored in site_config.json
{
  "knowlan": {
    "enable_performance_monitoring": true,
    "cache_ttl": 3600,
    "enable_graph_clustering": true,
    "max_graph_nodes": 1000,
    "default_layout": "spring",
    "enable_lazy_loading": true,
    "enable_virtual_scrolling": true
  }
}
```

### Boot Integration
```python
def boot_session(bootinfo):
    """Add Knowlan configuration to user session"""
    
    if not has_knowlan_access():
        return
    
    bootinfo["knowlan"] = {
        "config": get_knowlan_configuration(),
        "permissions": get_user_permissions(),
        "features": get_enabled_features()
    }
```

## Performance Optimizations

### 1. **Frontend Optimizations**
- **Lazy Loading**: Components load only when needed
- **Virtual Scrolling**: Handle large lists efficiently
- **Debounced Search**: Reduce API calls during typing
- **Memoization**: Cache expensive calculations
- **Asset Optimization**: Minified CSS/JS in production

### 2. **Backend Optimizations**
- **Intelligent Caching**: Multi-level caching with TTL
- **Query Optimization**: Database-specific query optimizations
- **Batch Processing**: Process large datasets in chunks
- **Connection Pooling**: Efficient database connections
- **Background Tasks**: Heavy processing in background

### 3. **Network Optimizations**
- **Compression**: Gzip compression for API responses
- **CDN Ready**: Static assets can be served from CDN
- **HTTP/2**: Support for modern HTTP protocols
- **Caching Headers**: Proper cache control headers

## Security Implementation

### 1. **Authentication & Authorization**
```python
@frappe.whitelist()
def secure_endpoint():
    """Example of secured endpoint"""
    
    # Check if user has permission
    if not frappe.has_permission("System Manager"):
        frappe.throw(_("Not permitted to access this resource"), 
                    frappe.PermissionError)
    
    # Additional role-based checks
    if not has_knowlan_access():
        frappe.throw(_("Knowlan access required"), 
                    frappe.PermissionError)
    
    return process_request()
```

### 2. **Input Validation**
```python
def validate_input(data):
    """Validate and sanitize user input"""
    
    # Type validation
    if not isinstance(data, dict):
        frappe.throw(_("Invalid data format"))
    
    # Required fields
    required_fields = ["doctype", "filters"]
    for field in required_fields:
        if field not in data:
            frappe.throw(_("Missing required field: {0}").format(field))
    
    # Sanitize strings
    for key, value in data.items():
        if isinstance(value, str):
            data[key] = frappe.utils.cstr(value).strip()
    
    return data
```

### 3. **SQL Injection Prevention**
```python
# Always use Frappe ORM methods
doctypes = frappe.get_all("DocType", 
                         filters={"custom": 0}, 
                         fields=["name", "module"])

# Never use raw SQL with user input
# BAD: frappe.db.sql(f"SELECT * FROM tabDocType WHERE name = '{user_input}'")
# GOOD: frappe.db.get_value("DocType", user_input, "name")
```

## Testing Architecture

### 1. **Unit Tests**
```python
class TestMetadataExtractor(FrappeTestCase):
    """Test metadata extraction functionality"""
    
    def setUp(self):
        self.extractor = MetadataExtractor()
    
    def test_extract_doctypes(self):
        """Test DocType extraction with mocked data"""
        with patch('frappe.get_all') as mock_get_all:
            mock_get_all.return_value = [
                {'name': 'User', 'module': 'Core', 'custom': 0}
            ]
            
            result = self.extractor.extract_doctypes()
            
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0]['name'], 'User')
```

### 2. **Integration Tests**
```python
class TestAPIIntegration(FrappeTestCase):
    """Test API endpoints integration"""
    
    def test_metadata_api_response(self):
        """Test metadata API returns valid response"""
        
        response = frappe.get_doc({
            "method": "knowlan.api.v1.metadata.get_doctypes",
            "args": {"include_custom": False}
        })
        
        self.assertIsInstance(response, dict)
        self.assertIn("doctypes", response)
        self.assertIn("statistics", response)
```

### 3. **Frontend Tests**
```javascript
// Jest/QUnit tests for frontend components
describe('SystemAtlas', function() {
    beforeEach(function() {
        this.atlas = new SystemAtlas();
    });
    
    it('should initialize cytoscape instance', function() {
        expect(this.atlas.cy).toBeDefined();
        expect(this.atlas.cy.nodes().length).toBeGreaterThan(0);
    });
    
    it('should handle layout changes', function() {
        this.atlas.change_layout('hierarchical');
        expect(this.atlas.current_layout).toBe('hierarchical');
    });
});
```

## Deployment Architecture

### 1. **Docker Configuration**
```dockerfile
FROM frappe/frappe-nginx:latest

# Copy app files
COPY . /home/<USER>/frappe-bench/apps/knowlan

# Install dependencies
RUN pip install -e /home/<USER>/frappe-bench/apps/knowlan

# Build assets
RUN bench build --app knowlan
```

### 2. **Kubernetes Deployment**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: knowlan-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: knowlan
  template:
    metadata:
      labels:
        app: knowlan
    spec:
      containers:
      - name: knowlan
        image: knowlan:latest
        ports:
        - containerPort: 8000
        env:
        - name: FRAPPE_SITE
          value: "your-site.com"
```

This architecture ensures Knowlan is scalable, maintainable, and follows Frappe best practices while providing a rich, interactive user experience.
