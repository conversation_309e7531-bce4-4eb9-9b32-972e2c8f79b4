# Distribution Standards and Best Practices

## Package Structure Standards

### 1. **Python Package Configuration**

#### setup.py
```python
from setuptools import setup, find_packages

with open("requirements.txt") as f:
    install_requires = f.read().strip().split("\n")

# get version from __version__ variable in knowlan/__init__.py
from knowlan import __version__ as version

setup(
    name="knowlan",
    version=version,
    description="Visual knowledge base system for Frappe/ERPNext instances",
    author="Frappe Technologies",
    author_email="<EMAIL>",
    packages=find_packages(),
    zip_safe=False,
    include_package_data=True,
    install_requires=install_requires
)
```

#### pyproject.toml (Modern Python Packaging)
```toml
[project]
name = "knowlan"
authors = [
    { name = "Frappe Technologies", email = "<EMAIL>"}
]
description = "Visual knowledge base system for Frappe/ERPNext instances"
requires-python = ">=3.10"
readme = "README.md"
dynamic = ["version"]
dependencies = [
    # "frappe~=15.0.0" # Installed and managed by bench.
]

[project.optional-dependencies]
advanced = [
    "networkx>=2.8",
    "leidenalg>=0.9.0",
    "python-igraph>=0.10.0",
    "psutil>=5.9.0",
]

[build-system]
requires = ["flit_core >=3.4,<4"]
build-backend = "flit_core.buildapi"
```

### 2. **Asset Management**

#### MANIFEST.in
```
include MANIFEST.in
include requirements.txt
include *.json
include *.md
include *.py
include *.txt
recursive-include knowlan *.css
recursive-include knowlan *.csv
recursive-include knowlan *.html
recursive-include knowlan *.ico
recursive-include knowlan *.js
recursive-include knowlan *.json
recursive-include knowlan *.md
recursive-include knowlan *.png
recursive-include knowlan *.jpg
recursive-include knowlan *.jpeg
recursive-include knowlan *.py
recursive-include knowlan *.svg
recursive-include knowlan *.txt
recursive-exclude knowlan *.pyc
```

**Key Addition**: Added `.jpg` and `.jpeg` extensions to include the app logo.

### 3. **App Metadata**

#### app.json (Frappe App Metadata)
```json
{
  "name": "knowlan",
  "title": "Knowlan",
  "version": "0.0.1",
  "description": "Visual knowledge base system for Frappe/ERPNext instances",
  "author": "Frappe Technologies",
  "author_email": "<EMAIL>",
  "license": "MIT",
  "repository": "https://github.com/frappe/knowlan",
  "homepage": "https://github.com/frappe/knowlan",
  "documentation": "https://github.com/frappe/knowlan/blob/main/README.md",
  "keywords": [
    "frappe", "erpnext", "knowledge-base", "visualization",
    "system-atlas", "metadata", "documentation"
  ],
  "categories": [
    "Developer Tools", "Documentation", "Visualization"
  ],
  "frappe_version": ">=15.0.0",
  "python_version": ">=3.10",
  "dependencies": {
    "frappe": ">=15.0.0"
  },
  "optional_dependencies": {
    "networkx": ">=2.8",
    "leidenalg": ">=0.9.0",
    "python-igraph": ">=0.10.0",
    "psutil": ">=5.9.0"
  },
  "features": [
    "System Atlas - Visual system architecture mapping",
    "Learning Classroom - Interactive documentation system",
    "Insights Dashboard - Performance and usage analytics",
    "Audit Dashboard - System health monitoring",
    "Developer Toolbox - Development utilities"
  ]
}
```

## Conventional Commit Standards

### Commit Types Used

#### **feat:** New Features
```
feat: initialize Knowlan app with basic structure
feat: add core Frappe app configuration
feat: implement installation and boot system
feat: implement comprehensive API layer
feat: add utility modules and background tasks
feat: add comprehensive frontend and backend components
feat: add containerization and deployment configurations
```

#### **docs:** Documentation
```
docs: add comprehensive documentation
```

#### **fix:** Bug Fixes
```
fix: resolve cytoscape null errors and prevent duplicate initializations
fix: resolve site rendering and API errors
fix: resolve installation errors and rename map to atlas
```

#### **chore:** Maintenance
```
chore: prepare app for distribution
```

#### **refactor:** Code Restructuring
```
refactor: rename all 'map' references to 'atlas' throughout codebase
```

### Commit Message Structure

```
<type>(<scope>): <subject>

<body>

<footer>
```

**Example:**
```
feat: implement installation and boot system

- Add comprehensive installation hooks with error handling
- Configure site-based settings instead of DocType dependency
- Implement boot session configuration for client-side data
- Add installation status tracking and configuration management
- Include proper cleanup hooks for uninstallation
```

## Licensing Standards

### MIT License
```
MIT License

Copyright (c) 2024 Frappe Technologies

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## Quality Assurance Standards

### 1. **Code Quality**
- **Error Handling**: Comprehensive try-catch blocks with proper logging
- **Type Hints**: Python type annotations where applicable
- **Documentation**: Docstrings for all public functions and classes
- **Testing**: Unit tests with >90% coverage

### 2. **Security**
- **Permission Checks**: Proper Frappe permission validation
- **Input Validation**: Sanitization of user inputs
- **SQL Injection Prevention**: Use of Frappe ORM methods
- **XSS Prevention**: Proper output escaping

### 3. **Performance**
- **Caching**: Intelligent caching with TTL
- **Query Optimization**: Efficient database queries
- **Lazy Loading**: Frontend components load on demand
- **Asset Optimization**: Minified CSS/JS for production

### 4. **Compatibility**
- **Python Version**: 3.10+ support
- **Frappe Version**: 15.0+ compatibility
- **Browser Support**: Modern browsers (Chrome, Firefox, Safari, Edge)
- **Mobile Responsive**: Works on mobile devices

## Distribution Checklist

- [x] **Package Structure**: Complete with setup.py, pyproject.toml, MANIFEST.in
- [x] **Dependencies**: Properly declared in requirements.txt
- [x] **Assets**: All CSS, JS, images included in package
- [x] **Documentation**: README, API docs, user guides complete
- [x] **License**: MIT license with proper attribution
- [x] **Version Control**: Clean git history with conventional commits
- [x] **Testing**: Comprehensive test suite included
- [x] **Configuration**: Site-based configuration system
- [x] **Installation**: Works with standard bench workflow
- [x] **Metadata**: Complete app.json with all required fields

## Installation Instructions for Users

### Standard Installation
```bash
# Get the app from repository
bench get-app https://github.com/your-org/knowlan

# Or from local path
bench get-app /path/to/knowlan

# Install on site
bench --site your-site install-app knowlan

# Start/restart bench
bench start
```

### Verification
After installation, verify Knowlan is working:
1. Login to your Frappe site
2. Look for "Knowlan" in the app launcher
3. Click to access the System Atlas interface
4. Check that all modules load without errors
