{"name": "knowlan", "title": "<PERSON><PERSON>", "version": "0.0.1", "description": "Visual knowledge base system for Frappe/ERPNext instances", "author": "Frappe Technologies", "author_email": "<EMAIL>", "license": "MIT", "repository": "https://github.com/frappe/knowlan", "homepage": "https://github.com/frappe/knowlan", "documentation": "https://github.com/frappe/knowlan/blob/main/README.md", "keywords": ["frappe", "erpnext", "knowledge-base", "visualization", "system-atlas", "metadata", "documentation"], "categories": ["Developer Tools", "Documentation", "Visualization"], "frappe_version": ">=15.0.0", "python_version": ">=3.10", "dependencies": {"frappe": ">=15.0.0"}, "optional_dependencies": {"networkx": ">=2.8", "leidenalg": ">=0.9.0", "python-igraph": ">=0.10.0", "psutil": ">=5.9.0"}, "features": ["System Atlas - Visual system architecture mapping", "Learning Classroom - Interactive documentation system", "Insights Dashboard - Performance and usage analytics", "Audit Dashboard - System health monitoring", "Developer Toolbox - Development utilities"], "screenshots": [], "demo_url": "", "support_email": "<EMAIL>", "tags": ["visualization", "documentation", "developer-tools", "system-mapping", "knowledge-management"]}