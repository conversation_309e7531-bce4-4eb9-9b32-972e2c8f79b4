// Copyright (c) 2024, Frappe Technologies and contributors
// License: MIT. See LICENSE

/**
 * Debug Playground Module for Knowlan
 * Trigger-based error capture and debugging workspace
 */

frappe.provide("knowlan.debug_playground");

knowlan.debug_playground = {

	// State
	container: null,
	error_tickets: new Map(), // In-memory storage
	current_ticket_id: null,

	// Initialize the debug playground
	init: function(container) {
		console.log('Debug Playground init called with container:', container);

		if (!container || !container.length) {
			console.error('Debug Playground: Invalid container provided');
			return;
		}

		this.container = container;
		this.setup_ui();
		this.setup_error_detection();
		this.setup_cleanup_timer();

		console.log('Debug Playground initialized (trigger-based)');
	},
	
	// Set up the main UI
	setup_ui: function() {
		var me = this;

		// Main container HTML - Simple workspace view
		var html = `
			<div class="debug-playground-container">
				<!-- Header -->
				<div class="debug-header">
					<div class="row">
						<div class="col-md-8">
							<h2><i class="fa fa-bug"></i> Debug Playground</h2>
							<p class="text-muted">Trigger-based error debugging workspace</p>
						</div>
						<div class="col-md-4 text-right">
							<button class="btn btn-primary btn-sm" id="lookup-ticket-btn">
								<i class="fa fa-search"></i> Lookup Ticket
							</button>
						</div>
					</div>
				</div>

				<!-- Main Content Area -->
				<div class="debug-main-content">
					<div class="debug-content-panel" id="debug-main-panel">
						<div class="welcome-section">
							<div class="text-center">
								<i class="fa fa-bug" style="font-size: 4em; color: #95a5a6; margin-bottom: 20px;"></i>
								<h3>Debug Playground</h3>
								<p class="text-muted">
									The Debug Playground activates when errors are detected.<br>
									When an error occurs, a "Create Ticket" button will appear in the top-right corner.
								</p>
								<div class="info-cards">
									<div class="row">
										<div class="col-md-4">
											<div class="info-card">
												<i class="fa fa-exclamation-triangle"></i>
												<h4>Error Detection</h4>
												<p>Automatic detection of JavaScript and Python errors</p>
											</div>
										</div>
										<div class="col-md-4">
											<div class="info-card">
												<i class="fa fa-ticket"></i>
												<h4>Create Ticket</h4>
												<p>Generate comprehensive error report with system context</p>
											</div>
										</div>
										<div class="col-md-4">
											<div class="info-card">
												<i class="fa fa-clock-o"></i>
												<h4>72-Hour Cleanup</h4>
												<p>Automatic cleanup of unclaimed error data</p>
											</div>
										</div>
									</div>
								</div>

								<div class="lookup-section" style="margin-top: 40px;">
									<h4>Lookup Existing Ticket</h4>
									<div class="input-group" style="max-width: 400px; margin: 0 auto;">
										<input type="text" class="form-control" id="ticket-id-input" placeholder="Enter Ticket ID (e.g., DBG-2025-001)">
										<span class="input-group-btn">
											<button class="btn btn-primary" id="lookup-btn">
												<i class="fa fa-search"></i> Lookup
											</button>
										</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		`;

		this.container.html(html);
		this.setup_event_handlers();
	},
	
	// Set up error detection system
	setup_error_detection: function() {
		var me = this;

		// Override console.error to detect errors
		var original_console_error = console.error;
		console.error = function() {
			// Call original console.error
			original_console_error.apply(console, arguments);

			// Show create ticket button
			me.show_create_ticket_button();
		};

		// Listen for JavaScript errors
		window.addEventListener('error', function(event) {
			me.show_create_ticket_button();
		});

		// Listen for unhandled promise rejections
		window.addEventListener('unhandledrejection', function(event) {
			me.show_create_ticket_button();
		});

		// Hook into Frappe's error handling
		if (frappe && frappe.throw) {
			var original_throw = frappe.throw;
			frappe.throw = function(msg, exception_type) {
				// Show create ticket button before throwing
				me.show_create_ticket_button();

				// Call original throw
				return original_throw.call(this, msg, exception_type);
			};
		}
	},
	
	// Show create ticket button next to About option in Knowlan header
	show_create_ticket_button: function() {
		// Check if button already exists
		if ($('#debug-create-ticket-btn').length > 0) {
			return;
		}

		// Find the Knowlan header area (look for About button or similar header elements)
		var header_target = null;

		// First, try to find Knowlan-specific header elements
		if ($('.knowlan-header .col-md-12').length > 0) {
			// Add to the main Knowlan header
			header_target = $('.knowlan-header .col-md-12');
		} else if ($('.knowlan-module-header .col-md-8').length > 0) {
			// Add to module header if in a specific module
			header_target = $('.knowlan-module-header .col-md-8');
		} else if ($('.knowlan-header').length > 0) {
			// Fallback to any Knowlan header
			header_target = $('.knowlan-header');
		} else if ($('[data-page-route="knowlan"] .page-head').length > 0) {
			header_target = $('[data-page-route="knowlan"] .page-head');
		} else if ($('.page-head .btn-group').length > 0) {
			header_target = $('.page-head .btn-group').first();
		} else if ($('.page-head .page-actions').length > 0) {
			header_target = $('.page-head .page-actions');
		} else if ($('.page-head').length > 0) {
			header_target = $('.page-head');
		} else if ($('.navbar-right').length > 0) {
			header_target = $('.navbar-right');
		}

		if (header_target) {
			// Create button to insert into header
			var button_html = '';

			// Different button styles based on header type
			if (header_target.hasClass('col-md-12') || header_target.hasClass('col-md-8')) {
				// For Knowlan headers, create a floating right button
				button_html = `
					<div class="debug-ticket-header-container" style="position: absolute; top: 10px; right: 15px;">
						<button id="debug-create-ticket-btn" class="btn btn-danger btn-sm debug-create-ticket-header">
							<i class="fa fa-bug"></i> Create Debug Ticket
						</button>
					</div>
				`;
				header_target.css('position', 'relative').append(button_html);
			} else {
				// For other headers, insert inline
				button_html = `
					<button id="debug-create-ticket-btn" class="btn btn-danger btn-sm debug-create-ticket-header" style="margin-left: 10px;">
						<i class="fa fa-bug"></i> Create Debug Ticket
					</button>
				`;

				if (header_target.hasClass('btn-group')) {
					header_target.after(button_html);
				} else {
					header_target.append(button_html);
				}
			}
		} else {
			// Fallback: create in a more generic header location
			var fallback_html = `
				<div id="debug-create-ticket-btn" class="debug-create-ticket-header-fallback">
					<button class="btn btn-danger btn-sm">
						<i class="fa fa-bug"></i> Create Debug Ticket
					</button>
				</div>
			`;

			// Try different fallback locations
			if ($('.container').first().length > 0) {
				$('.container').first().prepend(fallback_html);
			} else {
				$('body').prepend(fallback_html);
			}
		}

		// Add click handler
		var me = this;
		$('#debug-create-ticket-btn').on('click', function() {
			me.create_error_ticket();
			me.remove_create_ticket_button(); // Remove button after use
		});

		// Auto-remove after 30 seconds if not used
		setTimeout(function() {
			me.remove_create_ticket_button();
		}, 30000);
	},

	// Remove create ticket button
	remove_create_ticket_button: function() {
		$('#debug-create-ticket-btn').fadeOut(function() {
			$(this).closest('.debug-ticket-header-container').remove();
			$(this).remove();
		});
	},

	// Create error ticket with comprehensive system info
	create_error_ticket: function() {
		var me = this;

		// Generate unique ticket ID
		var ticket_id = this.generate_ticket_id();

		// Collect comprehensive system information
		var ticket_data = this.collect_system_info();
		ticket_data.ticket_id = ticket_id;
		ticket_data.created_at = new Date().toISOString();

		// Store in memory (with 72-hour expiry)
		this.error_tickets.set(ticket_id, ticket_data);

		// Show ticket modal
		this.show_ticket_modal(ticket_data);
	},
	
	// Collect comprehensive system information
	collect_system_info: function() {
		var info = {
			// Basic Information
			timestamp: new Date().toISOString(),
			user: frappe.session.user,
			session_id: frappe.session.sid,

			// Module and DocType Context
			module: this.get_current_module(),
			doctype: this.get_current_doctype(),
			document_name: this.get_current_document_name(),

			// Process Context
			process: this.get_current_process(),

			// User and System Context
			user_tag: this.get_user_tag(),

			// Document History
			document_history: this.get_document_history(),

			// Error Information
			error_log: this.get_recent_errors(),

			// Code Context
			error_paths: this.get_error_paths(),
			functions_involved: this.get_functions_involved(),
			fields_involved: this.get_fields_involved(),

			// Browser and System Info
			browser_info: this.get_browser_info(),
			screen_info: this.get_screen_info(),

			// Current Route and Context
			route: frappe.get_route_str(),
			url: window.location.href,

			// Form Context (if applicable)
			form_context: this.get_form_context(),

			// Network Activity (recent)
			network_activity: this.get_recent_network_activity(),

			// Performance Metrics
			performance_info: this.get_performance_info()
		};

		return info;
	},
	
	// Helper methods for collecting system information
	get_current_module: function() {
		var route = frappe.get_route();
		if (route && route.length > 0) {
			// Try to determine module from route
			if (route[0] === 'app' && route[1]) {
				return route[1];
			}
		}
		return 'Unknown';
	},

	get_current_doctype: function() {
		if (cur_frm && cur_frm.doctype) {
			return cur_frm.doctype;
		}

		var route = frappe.get_route();
		if (route && route[0] === 'Form' && route[1]) {
			return route[1];
		}

		return 'Unknown';
	},

	get_current_document_name: function() {
		if (cur_frm && cur_frm.docname) {
			return cur_frm.docname;
		}

		var route = frappe.get_route();
		if (route && route[0] === 'Form' && route[2]) {
			return route[2];
		}

		return 'Unknown';
	},

	get_current_process: function() {
		// Try to determine current process context
		if (frappe.dom.freeze_count > 0) {
			return 'Loading/Processing';
		}

		if (cur_frm) {
			if (cur_frm.__islocal) {
				return 'OnLoad (New Document)';
			} else {
				return 'OnLoad (Existing Document)';
			}
		}

		return 'Page Load';
	},

	get_user_tag: function() {
		var user_roles = frappe.user_roles || [];
		var primary_role = user_roles.length > 0 ? user_roles[0] : 'Unknown';

		return {
			user: frappe.session.user,
			roles: user_roles,
			primary_role: primary_role,
			is_system_manager: user_roles.includes('System Manager'),
			is_administrator: frappe.session.user === 'Administrator'
		};
	},
	
	get_document_history: function() {
		if (!cur_frm || !cur_frm.doc) {
			return 'No document context';
		}

		var doc = cur_frm.doc;
		var history = {
			creation: doc.creation,
			modified: doc.modified,
			modified_by: doc.modified_by,
			owner: doc.owner,
			docstatus: doc.docstatus,
			workflow_state: doc.workflow_state,
			recent_changes: []
		};

		// Get recent changes if available
		if (cur_frm.timeline && cur_frm.timeline.timeline_items) {
			history.recent_changes = cur_frm.timeline.timeline_items.slice(0, 5);
		}

		return history;
	},

	get_recent_errors: function() {
		// Capture recent console errors (if any were logged)
		var errors = [];

		// Check if there are any recent Frappe messages
		if (frappe.messages && frappe.messages.length > 0) {
			errors = frappe.messages.slice(-5).map(function(msg) {
				return {
					type: 'frappe_message',
					message: msg.message,
					indicator: msg.indicator,
					timestamp: new Date().toISOString()
				};
			});
		}

		return errors;
	},

	get_error_paths: function() {
		// Try to capture current script paths and stack traces
		var paths = [];

		try {
			throw new Error('Stack trace capture');
		} catch (e) {
			if (e.stack) {
				var stack_lines = e.stack.split('\n');
				paths = stack_lines.slice(1, 6).map(function(line) {
					var match = line.match(/at\s+(.+)\s+\((.+):(\d+):(\d+)\)/);
					if (match) {
						return {
							function: match[1],
							file: match[2],
							line: match[3],
							column: match[4]
						};
					}
					return line.trim();
				});
			}
		}

		return paths;
	},
	
	get_functions_involved: function() {
		var functions = [];

		// Get current form methods if in a form
		if (cur_frm) {
			functions.push({
				context: 'form',
				methods: Object.getOwnPropertyNames(cur_frm).filter(function(prop) {
					return typeof cur_frm[prop] === 'function';
				}).slice(0, 10) // Limit to first 10
			});
		}

		// Get current page methods
		if (frappe.cur_page) {
			functions.push({
				context: 'page',
				methods: Object.getOwnPropertyNames(frappe.cur_page).filter(function(prop) {
					return typeof frappe.cur_page[prop] === 'function';
				}).slice(0, 10)
			});
		}

		return functions;
	},

	get_fields_involved: function() {
		if (!cur_frm || !cur_frm.fields_dict) {
			return [];
		}

		var fields = [];
		Object.keys(cur_frm.fields_dict).forEach(function(fieldname) {
			var field = cur_frm.fields_dict[fieldname];
			fields.push({
				fieldname: fieldname,
				fieldtype: field.df.fieldtype,
				label: field.df.label,
				value: cur_frm.doc[fieldname],
				hidden: field.df.hidden,
				read_only: field.df.read_only
			});
		});

		return fields.slice(0, 20); // Limit to first 20 fields
	},
	
	get_browser_info: function() {
		return {
			user_agent: navigator.userAgent,
			language: navigator.language,
			platform: navigator.platform,
			cookie_enabled: navigator.cookieEnabled,
			online: navigator.onLine,
			timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
		};
	},

	get_screen_info: function() {
		return {
			screen_width: screen.width,
			screen_height: screen.height,
			viewport_width: window.innerWidth,
			viewport_height: window.innerHeight,
			color_depth: screen.colorDepth,
			pixel_ratio: window.devicePixelRatio
		};
	},

	get_form_context: function() {
		if (!cur_frm) {
			return null;
		}

		return {
			doctype: cur_frm.doctype,
			docname: cur_frm.docname,
			is_new: cur_frm.is_new(),
			is_dirty: cur_frm.is_dirty(),
			docstatus: cur_frm.doc.docstatus,
			meta: {
				title_field: cur_frm.meta.title_field,
				has_workflow: cur_frm.meta.has_workflow,
				is_submittable: cur_frm.meta.is_submittable
			}
		};
	},
	
	get_recent_network_activity: function() {
		// Simple network activity capture (last few requests)
		var activity = [];

		if (window.performance && window.performance.getEntriesByType) {
			var entries = window.performance.getEntriesByType('navigation');
			if (entries.length > 0) {
				var nav = entries[0];
				activity.push({
					type: 'navigation',
					duration: nav.loadEventEnd - nav.fetchStart,
					dns_time: nav.domainLookupEnd - nav.domainLookupStart,
					connect_time: nav.connectEnd - nav.connectStart,
					response_time: nav.responseEnd - nav.responseStart
				});
			}
		}

		return activity;
	},

	get_performance_info: function() {
		var perf = {
			timestamp: Date.now(),
			memory: null,
			timing: null
		};

		// Memory info (if available)
		if (window.performance && window.performance.memory) {
			perf.memory = {
				used: window.performance.memory.usedJSHeapSize,
				total: window.performance.memory.totalJSHeapSize,
				limit: window.performance.memory.jsHeapSizeLimit
			};
		}

		// Timing info
		if (window.performance && window.performance.timing) {
			var timing = window.performance.timing;
			perf.timing = {
				page_load: timing.loadEventEnd - timing.navigationStart,
				dom_ready: timing.domContentLoadedEventEnd - timing.navigationStart,
				first_paint: timing.responseEnd - timing.requestStart
			};
		}

		return perf;
	},
	
	// Generate unique ticket ID
	generate_ticket_id: function() {
		var now = new Date();
		var year = now.getFullYear();
		var month = String(now.getMonth() + 1).padStart(2, '0');
		var day = String(now.getDate()).padStart(2, '0');
		var time = String(now.getHours()).padStart(2, '0') +
				   String(now.getMinutes()).padStart(2, '0') +
				   String(now.getSeconds()).padStart(2, '0');

		return `DBG-${year}-${month}${day}-${time}`;
	},

	// Show ticket modal with comprehensive information
	show_ticket_modal: function(ticket_data) {
		var me = this;

		// Format ticket data as readable text
		var formatted_text = this.format_ticket_data(ticket_data);

		// Create modal
		var modal = new frappe.ui.Dialog({
			title: 'Debug Ticket Created: ' + ticket_data.ticket_id,
			fields: [
				{
					fieldtype: 'HTML',
					fieldname: 'ticket_info',
					options: `
						<div class="ticket-modal-content">
							<div class="alert alert-success">
								<strong>Debug Ticket Created Successfully!</strong><br>
								Ticket ID: <code>${ticket_data.ticket_id}</code><br>
								This ticket will be available for 72 hours.
							</div>
							<div class="ticket-data-container">
								<textarea class="form-control" rows="20" readonly style="font-family: monospace; font-size: 12px;">${formatted_text}</textarea>
							</div>
						</div>
					`
				}
			],
			primary_action_label: 'Copy to Clipboard',
			primary_action: function() {
				me.copy_to_clipboard(formatted_text);
				frappe.show_alert('Ticket data copied to clipboard!');
			},
			secondary_action_label: 'Export as File',
			secondary_action: function() {
				me.export_ticket_data(ticket_data.ticket_id, formatted_text);
			}
		});

		modal.show();
	},
	
	// Format ticket data as readable text
	format_ticket_data: function(data) {
		var text = '';

		text += '='.repeat(80) + '\n';
		text += 'DEBUG TICKET: ' + data.ticket_id + '\n';
		text += '='.repeat(80) + '\n\n';

		text += 'BASIC INFORMATION\n';
		text += '-'.repeat(40) + '\n';
		text += 'Timestamp: ' + data.timestamp + '\n';
		text += 'User: ' + data.user + '\n';
		text += 'Session ID: ' + data.session_id + '\n';
		text += 'URL: ' + data.url + '\n';
		text += 'Route: ' + data.route + '\n\n';

		text += 'MODULE & DOCTYPE CONTEXT\n';
		text += '-'.repeat(40) + '\n';
		text += 'Module: ' + data.module + '\n';
		text += 'DocType: ' + data.doctype + '\n';
		text += 'Document Name: ' + data.document_name + '\n';
		text += 'Process: ' + data.process + '\n\n';

		text += 'USER CONTEXT\n';
		text += '-'.repeat(40) + '\n';
		text += 'User: ' + data.user_tag.user + '\n';
		text += 'Primary Role: ' + data.user_tag.primary_role + '\n';
		text += 'All Roles: ' + data.user_tag.roles.join(', ') + '\n';
		text += 'System Manager: ' + (data.user_tag.is_system_manager ? 'Yes' : 'No') + '\n\n';

		if (data.document_history && typeof data.document_history === 'object') {
			text += 'DOCUMENT HISTORY\n';
			text += '-'.repeat(40) + '\n';
			text += 'Created: ' + (data.document_history.creation || 'N/A') + '\n';
			text += 'Modified: ' + (data.document_history.modified || 'N/A') + '\n';
			text += 'Modified By: ' + (data.document_history.modified_by || 'N/A') + '\n';
			text += 'Owner: ' + (data.document_history.owner || 'N/A') + '\n';
			text += 'Status: ' + (data.document_history.docstatus || 'N/A') + '\n\n';
		}

		if (data.error_log && data.error_log.length > 0) {
			text += 'ERROR LOG\n';
			text += '-'.repeat(40) + '\n';
			data.error_log.forEach(function(error, index) {
				text += (index + 1) + '. ' + error.message + ' (' + error.type + ')\n';
			});
			text += '\n';
		}

		if (data.error_paths && data.error_paths.length > 0) {
			text += 'ERROR PATHS\n';
			text += '-'.repeat(40) + '\n';
			data.error_paths.forEach(function(path, index) {
				if (typeof path === 'object') {
					text += (index + 1) + '. ' + path.function + ' (' + path.file + ':' + path.line + ')\n';
				} else {
					text += (index + 1) + '. ' + path + '\n';
				}
			});
			text += '\n';
		}

		return text;
	},
	
	// Copy text to clipboard
	copy_to_clipboard: function(text) {
		if (navigator.clipboard && navigator.clipboard.writeText) {
			navigator.clipboard.writeText(text);
		} else {
			// Fallback for older browsers
			var textArea = document.createElement('textarea');
			textArea.value = text;
			document.body.appendChild(textArea);
			textArea.select();
			document.execCommand('copy');
			document.body.removeChild(textArea);
		}
	},

	// Export ticket data as file
	export_ticket_data: function(ticket_id, data) {
		var blob = new Blob([data], { type: 'text/plain' });
		var url = window.URL.createObjectURL(blob);
		var a = document.createElement('a');
		a.href = url;
		a.download = ticket_id + '_debug_ticket.txt';
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		window.URL.revokeObjectURL(url);
	},

	// Lookup ticket by ID
	lookup_ticket: function(ticket_id) {
		if (this.error_tickets.has(ticket_id)) {
			var ticket_data = this.error_tickets.get(ticket_id);
			this.show_ticket_details(ticket_data);
		} else {
			frappe.msgprint('Ticket not found or has expired (72-hour limit)');
		}
	},

	// Show ticket details in workspace
	show_ticket_details: function(ticket_data) {
		var main_panel = this.container.find('#debug-main-panel');

		var html = `
			<div class="ticket-details-view">
				<div class="ticket-header">
					<h3><i class="fa fa-bug"></i> ${ticket_data.ticket_id}</h3>
					<div class="ticket-meta">
						<span class="badge">Created: ${new Date(ticket_data.created_at).toLocaleString()}</span>
						<span class="badge">User: ${ticket_data.user}</span>
						<span class="badge">Module: ${ticket_data.module}</span>
					</div>
				</div>

				<div class="ticket-content">
					<pre class="ticket-data">${this.format_ticket_data(ticket_data)}</pre>
				</div>

				<div class="ticket-actions">
					<button class="btn btn-primary" onclick="knowlan.debug_playground.copy_to_clipboard('${this.format_ticket_data(ticket_data).replace(/'/g, "\\'")}')">
						<i class="fa fa-copy"></i> Copy to Clipboard
					</button>
					<button class="btn btn-default" onclick="knowlan.debug_playground.export_ticket_data('${ticket_data.ticket_id}', '${this.format_ticket_data(ticket_data).replace(/'/g, "\\'")}')">
						<i class="fa fa-download"></i> Export
					</button>
					<button class="btn btn-default" onclick="knowlan.debug_playground.show_welcome()">
						<i class="fa fa-arrow-left"></i> Back
					</button>
				</div>
			</div>
		`;

		main_panel.html(html);
	},
	
	// Show welcome screen
	show_welcome: function() {
		this.setup_ui(); // Reset to welcome screen
	},

	// Set up cleanup timer (72-hour expiry)
	setup_cleanup_timer: function() {
		var me = this;

		// Run cleanup every hour
		setInterval(function() {
			me.cleanup_expired_tickets();
		}, 3600000); // 1 hour

		// Run initial cleanup
		this.cleanup_expired_tickets();
	},

	// Clean up expired tickets (72 hours)
	cleanup_expired_tickets: function() {
		var now = new Date();
		var expired_tickets = [];

		this.error_tickets.forEach(function(ticket_data, ticket_id) {
			var created_at = new Date(ticket_data.created_at);
			var hours_old = (now - created_at) / (1000 * 60 * 60);

			if (hours_old > 72) {
				expired_tickets.push(ticket_id);
			}
		});

		// Remove expired tickets
		expired_tickets.forEach(function(ticket_id) {
			this.error_tickets.delete(ticket_id);
		}.bind(this));

		if (expired_tickets.length > 0) {
			console.log('Cleaned up ' + expired_tickets.length + ' expired debug tickets');
		}
	},
	
	// Set up event handlers
	setup_event_handlers: function() {
		var me = this;

		// Lookup ticket button
		this.container.find('#lookup-ticket-btn, #lookup-btn').on('click', function() {
			var ticket_id = me.container.find('#ticket-id-input').val().trim();
			if (ticket_id) {
				me.lookup_ticket(ticket_id);
			} else {
				frappe.msgprint('Please enter a ticket ID');
			}
		});

		// Enter key on ticket ID input
		this.container.find('#ticket-id-input').on('keypress', function(e) {
			if (e.which === 13) {
				var ticket_id = $(this).val().trim();
				if (ticket_id) {
					me.lookup_ticket(ticket_id);
				}
			}
		});
	},
	
	// Cleanup
	cleanup: function() {
		// Remove any debug buttons
		$('#debug-create-ticket-btn').remove();
		$('.debug-ticket-header-container').remove();
		$('.debug-create-ticket-header-fallback').remove();

		// Clear timers
		if (this.cleanup_timer) {
			clearInterval(this.cleanup_timer);
		}

		// Clear container
		if (this.container) {
			this.container.empty();
		}

		console.log('Debug Playground cleaned up');
	}
};
