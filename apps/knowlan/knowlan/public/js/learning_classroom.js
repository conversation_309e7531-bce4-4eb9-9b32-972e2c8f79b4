// Copyright (c) 2024, Frappe Technologies and contributors
// License: MIT. See LICENSE

// Ensure knowlan namespace exists
if (typeof knowlan === 'undefined') {
	window.knowlan = {};
}

frappe.provide("knowlan.learning_classroom");

knowlan.learning_classroom = {
	init: function(container) {
		this.container = container;
		this.monaco_editor = null;
		this.current_path = [];
		this.notes = {};
		this.setup_ui();
		this.load_monaco_editor();
	},

	setup_ui: function() {
		var me = this;
		
		// Clear container
		this.container.empty();
		
		// Create main layout
		this.container.html(`
			<div class="learning-classroom-container">
				<div class="classroom-sidebar">
					<div class="sidebar-header">
						<h4><i class="fa fa-graduation-cap"></i> Classroom</h4>
						<div class="search-box">
							<div id="doctype-search-wrapper"></div>
						</div>
					</div>
					<div class="sidebar-content">
						<div class="roadmap-tree" id="roadmap-tree">
							<div class="loading-indicator">
								<i class="fa fa-spinner fa-spin"></i> Loading...
							</div>
						</div>
					</div>
				</div>
				
				<div class="classroom-main">
					<div class="main-header">
						<div class="breadcrumb-container">
							<nav aria-label="breadcrumb">
								<ol class="breadcrumb" id="classroom-breadcrumb">
									<li class="breadcrumb-item active">Select an App, Module or DocType to Explore</li>
								</ol>
							</nav>
						</div>
						<div class="main-controls">
							<div class="btn-group btn-group-sm" role="group">
								<button type="button" class="btn btn-default" id="view-overview">
									<i class="fa fa-info-circle"></i> Overview
								</button>
								<button type="button" class="btn btn-default" id="view-fields">
									<i class="fa fa-list"></i> Fields
								</button>
								<button type="button" class="btn btn-default" id="view-code">
									<i class="fa fa-code"></i> Code
								</button>
								<button type="button" class="btn btn-default" id="view-links">
									<i class="fa fa-link"></i> Links
								</button>
								<button type="button" class="btn btn-default" id="view-notes">
									<i class="fa fa-sticky-note"></i> Notes
								</button>
								<button type="button" class="btn btn-primary" id="open-doctype" style="display: none;">
									<i class="fa fa-external-link"></i> Open DocType
								</button>
							</div>
						</div>
					</div>
					
					<div class="main-content">
						<div class="content-panel" id="overview-panel">
							<div class="welcome-message">
								<div class="text-center">
									<i class="fa fa-graduation-cap fa-3x text-muted"></i>
									<h3>Welcome to Learning Classroom</h3>
									<p class="text-muted">
										Explore your Frappe system structure through guided learning paths.
										Select an app or module from the sidebar to begin your journey.
									</p>
								</div>
							</div>
						</div>
						
						<div class="content-panel" id="fields-panel" style="display: none;">
							<div class="fields-content">
								<!-- Fields will be loaded here -->
							</div>
						</div>
						
						<div class="content-panel" id="code-panel" style="display: none;">
							<div class="code-viewer-container">
								<div class="code-viewer-header">
									<div class="file-tabs" id="file-tabs">
										<!-- File tabs will be added here -->
									</div>
									<div class="code-controls">
										<button class="btn btn-default btn-sm" id="format-code">
											<i class="fa fa-magic"></i> Format
										</button>
										<button class="btn btn-default btn-sm" id="copy-code">
											<i class="fa fa-copy"></i> Copy
										</button>
									</div>
								</div>
								<div class="code-editor" id="monaco-container">
									<!-- Monaco Editor will be initialized here -->
								</div>
							</div>
						</div>

						<div class="content-panel" id="links-panel" style="display: none;">
							<div class="links-container">
								<div class="links-header">
									<h5><i class="fa fa-link"></i> DocType Links</h5>
								</div>
								<div class="links-content">
									<div class="row">
										<div class="col-md-6">
											<div class="links-section">
												<h6><i class="fa fa-arrow-right text-success"></i> Outgoing Links</h6>
												<div id="outgoing-links" class="links-list"></div>
											</div>
										</div>
										<div class="col-md-6">
											<div class="links-section">
												<h6><i class="fa fa-arrow-left text-primary"></i> Incoming Links</h6>
												<div id="incoming-links" class="links-list"></div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="content-panel" id="notes-panel" style="display: none;">
							<div class="notes-container">
								<div class="notes-header">
									<div class="notes-title-section">
										<h5>Personal Notes</h5>
										<div class="notes-controls">
											<button class="btn btn-secondary btn-sm" id="toggle-preview">
												<i class="fa fa-eye"></i> Preview
											</button>
											<button class="btn btn-primary btn-sm" id="save-notes">
												<i class="fa fa-save"></i> Save
											</button>
											<button class="btn btn-default btn-sm" id="export-notes">
												<i class="fa fa-download"></i> Export
											</button>
										</div>
									</div>
									<div class="notes-toolbar">
										<div class="notes-mode-toggle">
											<button class="btn btn-sm btn-primary" id="edit-mode" title="Edit Mode">
												<i class="fa fa-edit"></i> Edit
											</button>
											<button class="btn btn-sm btn-outline-primary" id="split-mode" title="Split View">
												<i class="fa fa-columns"></i> Split
											</button>
											<button class="btn btn-sm btn-outline-primary" id="view-mode" title="View Mode">
												<i class="fa fa-eye"></i> View
											</button>
										</div>
										<div class="formatting-buttons">
											<button class="btn btn-sm btn-outline-secondary" data-format="bold" title="Bold">
												<i class="fa fa-bold"></i>
											</button>
											<button class="btn btn-sm btn-outline-secondary" data-format="italic" title="Italic">
												<i class="fa fa-italic"></i>
											</button>
											<button class="btn btn-sm btn-outline-secondary" data-format="underline" title="Underline">
												<i class="fa fa-underline"></i>
											</button>
											<button class="btn btn-sm btn-outline-secondary" data-format="highlight" title="Highlight">
												<i class="fa fa-paint-brush"></i>
											</button>
											<button class="btn btn-sm btn-outline-secondary" data-format="heading" title="Heading">
												<i class="fa fa-header"></i>
											</button>
											<button class="btn btn-sm btn-outline-secondary" data-format="code" title="Inline Code">
												<i class="fa fa-code"></i>
											</button>
											<button class="btn btn-sm btn-outline-secondary" data-format="codeblock" title="Code Block">
												<i class="fa fa-file-code-o"></i>
											</button>
											<button class="btn btn-sm btn-outline-secondary" data-format="link" title="Link">
												<i class="fa fa-link"></i>
											</button>
											<button class="btn btn-sm btn-outline-secondary" data-format="list" title="List">
												<i class="fa fa-list-ul"></i>
											</button>
											<button class="btn btn-sm btn-outline-secondary" data-format="table" title="Table">
												<i class="fa fa-table"></i>
											</button>
											<button class="btn btn-sm btn-outline-secondary" data-format="line" title="Horizontal Line">
												<i class="fa fa-minus"></i>
											</button>
										</div>
										<div class="notes-search">
											<button class="btn btn-sm btn-outline-secondary" id="find-replace-btn" title="Find & Replace">
												<i class="fa fa-search"></i>
											</button>
										</div>
									</div>
								</div>
								<div class="notes-main-area">
									<div class="notes-editor-container">
										<div class="notes-editor resizable-notes">
											<textarea class="form-control notes-textarea" id="notes-textarea"
													  placeholder="Add your notes here... (Markdown supported)"></textarea>
										</div>
										<div class="notes-sidebar" id="notes-sidebar">
											<div class="sidebar-header">
												<h6>Pages & TOC</h6>
												<div class="sidebar-controls">
													<button class="btn btn-sm btn-outline-success" id="add-page" title="Add New Page">
														<i class="fa fa-plus"></i>
													</button>
													<button class="btn btn-sm btn-outline-primary" id="reload-toc" title="Reload Table of Contents">
														<i class="fa fa-refresh"></i>
													</button>
													<button class="btn btn-sm btn-outline-secondary" id="toggle-sidebar" title="Hide Table of Contents">
														<i class="fa fa-times"></i>
													</button>
												</div>
											</div>
											<div class="pages-section" id="pages-section">
												<div class="pages-header">
													<small class="text-muted">Pages</small>
												</div>
												<div class="pages-list" id="pages-list">
													<!-- Pages will be listed here -->
												</div>
											</div>
											<div class="toc-section">
												<div class="toc-header">
													<small class="text-muted">Table of Contents</small>
												</div>
												<div class="toc-content" id="toc-content">
													<!-- Table of contents will be generated here -->
												</div>
											</div>
										</div>
									</div>
									<div class="notes-preview" id="notes-preview" style="display: none;">
										<!-- Markdown preview will be rendered here -->
									</div>
								</div>
								<div class="find-replace-panel" id="find-replace-panel" style="display: none;">
									<div class="find-replace-controls">
										<input type="text" class="form-control form-control-sm" id="find-input" placeholder="Find...">
										<input type="text" class="form-control form-control-sm" id="replace-input" placeholder="Replace...">
										<button class="btn btn-sm btn-primary" id="find-next">Find Next</button>
										<button class="btn btn-sm btn-secondary" id="replace-btn">Replace</button>
										<button class="btn btn-sm btn-secondary" id="replace-all">Replace All</button>
										<button class="btn btn-sm btn-outline-secondary" id="close-find-replace">
											<i class="fa fa-times"></i>
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		`);

		// Setup Link fields
		this.setup_link_fields();

		// Setup event handlers
		this.setup_event_handlers();

		// Load initial data
		this.load_roadmap_data();
	},

	setup_link_fields: function() {
		var me = this;

		// Setup DocType Link field for search
		var doctype_wrapper = this.container.find('#doctype-search-wrapper');
		if (doctype_wrapper.length) {
			this.doctype_search_field = frappe.ui.form.make_control({
				parent: doctype_wrapper[0],
				df: {
					fieldtype: 'Link',
					options: 'DocType',
					placeholder: 'Search DocTypes...',
					onchange: function() {
						var selected_doctype = me.doctype_search_field.get_value();
						if (selected_doctype) {
							me.select_doctype_from_search(selected_doctype);
						}
					}
				},
				render_input: true
			});

			// Style the field to match other controls
			doctype_wrapper.find('.form-group').css({
				'margin-bottom': '0',
				'width': '100%'
			});
			doctype_wrapper.find('.control-input').addClass('form-control-sm');
		}
	},

	setup_event_handlers: function() {
		var me = this;
		
		// View switching
		this.container.find('#view-overview').on('click', function() {
			me.show_panel('overview');
			me.set_active_view($(this));
		});
		
		this.container.find('#view-fields').on('click', function() {
			me.show_panel('fields');
			me.set_active_view($(this));
		});
		
		this.container.find('#view-code').on('click', function() {
			me.show_panel('code');
			me.set_active_view($(this));
		});
		
		this.container.find('#view-links').on('click', function() {
			me.show_panel('links');
			me.set_active_view($(this));
		});

		this.container.find('#view-notes').on('click', function() {
			me.show_panel('notes');
			me.set_active_view($(this));
		});

		// Open DocType button
		this.container.find('#open-doctype').on('click', function() {
			if (me.current_doctype) {
				me.open_doctype_form(me.current_doctype);
			}
		});

		// Search functionality is now handled by the Link field onchange event
		
		// Code controls
		this.container.find('#format-code').on('click', function() {
			me.format_code();
		});
		
		this.container.find('#copy-code').on('click', function() {
			me.copy_code();
		});
		
		// Notes controls
		this.container.find('#save-notes').on('click', function() {
			me.save_notes();
		});

		this.container.find('#export-notes').on('click', function() {
			me.export_notes();
		});

		// Setup markdown formatting buttons
		this.container.find('[data-format]').on('click', function() {
			var format = $(this).data('format');
			me.apply_markdown_format(format);
		});

		// Setup find and replace
		this.container.find('#find-replace-btn').on('click', function() {
			me.toggle_find_replace();
		});

		this.container.find('#close-find-replace').on('click', function() {
			me.container.find('#find-replace-panel').hide();
		});

		this.container.find('#find-next').on('click', function() {
			me.find_next();
		});

		this.container.find('#replace-btn').on('click', function() {
			me.replace_current();
		});

		this.container.find('#replace-all').on('click', function() {
			me.replace_all();
		});

		// Setup preview toggle
		this.container.find('#toggle-preview').on('click', function() {
			me.toggle_preview();
		});

		// Setup sidebar toggle
		this.container.find('#toggle-sidebar').on('click', function() {
			me.toggle_sidebar();
		});

		// Setup TOC reload
		this.container.find('#reload-toc').on('click', function() {
			me.generate_table_of_contents();
			frappe.show_alert({
				message: 'Table of Contents reloaded',
				indicator: 'green'
			}, 2);
		});

		// Setup page management
		this.container.find('#add-page').on('click', function() {
			me.show_add_page_modal();
		});

		// Setup Obsidian-style linking and live preview
		this.container.find('#notes-textarea').on('input', function() {
			me.handle_obsidian_linking();
			if (me.notes_mode === 'split') {
				me.update_live_preview();
			}
		});

		// Setup mode toggle
		this.container.find('#edit-mode').on('click', function() {
			me.switch_to_edit_mode();
		});

		this.container.find('#view-mode').on('click', function() {
			me.switch_to_view_mode();
		});

		this.container.find('#split-mode').on('click', function() {
			me.switch_to_split_mode();
		});

		// Setup textarea change handler for TOC generation
		this.container.find('#notes-textarea').on('input', function() {
			me.generate_table_of_contents();
		});
		
		// Hide all tabs initially
		this.hide_all_tabs();

		// Restore previous selection state if available
		this.restore_selection_state();

		// Set default active view (but keep hidden until selection)
		this.container.find('#view-overview').addClass('btn-primary').removeClass('btn-default');
	},

	load_monaco_editor: function() {
		var me = this;
		
		// Load Monaco Editor from CDN
		frappe.require([
			'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs/loader.js'
		], function() {
			require.config({ 
				paths: { 
					'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs' 
				}
			});
			
			require(['vs/editor/editor.main'], function() {
				me.init_monaco_editor();
			});
		});
	},

	init_monaco_editor: function() {
		var me = this;
		
		// Initialize Monaco Editor
		this.monaco_editor = monaco.editor.create(
			this.container.find('#monaco-container')[0], 
			{
				value: '# Select a DocType to view its code\n\n# Code will be displayed here',
				language: 'python',
				theme: 'vs-light',
				readOnly: true,
				minimap: { enabled: false },
				scrollBeyondLastLine: false,
				fontSize: 14,
				lineNumbers: 'on',
				wordWrap: 'on'
			}
		);
		
		// Handle editor resize
		window.addEventListener('resize', function() {
			if (me.monaco_editor) {
				me.monaco_editor.layout();
			}
		});
	},

	load_roadmap_data: function() {
		var me = this;
		
		// Load apps and modules structure
		frappe.call({
			method: 'knowlan.api.v1.metadata.get_apps',
			callback: function(r) {
				if (r.message && r.message.apps) {
					me.render_roadmap_tree(r.message.apps);
				}
			}
		});
	},

	render_roadmap_tree: function(apps) {
		var me = this;
		var completed_apps = 0;
		var total_apps = apps.length;
		var all_tree_html = '';

		apps.forEach(function(app) {
			var module_count = app.modules ? app.modules.length : 0;
			var app_completion = me.get_app_completion_status(app.name);
			var app_html = `
				<div class="tree-node app-node" data-type="app" data-name="${app.name}">
					<div class="node-header">
						<input type="checkbox" class="completion-checkbox app-checkbox" data-type="app" data-name="${app.name}" ${app_completion ? 'checked' : ''}>
						<i class="fa fa-folder tree-icon"></i>
						<span class="node-title">${app.name}</span>
						<span class="node-count">(${module_count} Modules, ${app.doctype_count} DocTypes)</span>
					</div>
					<div class="node-children" style="display: none;">
			`;

			// Group DocTypes by module
			var modules = {};
			app.modules.forEach(function(module_name) {
				modules[module_name] = [];
			});

			// Load DocTypes for this app
			frappe.call({
				method: 'knowlan.api.v1.metadata.get_doctypes',
				args: { app: app.name },
				callback: function(r) {
					if (r.message && r.message.doctypes) {
						r.message.doctypes.forEach(function(doctype) {
							if (modules[doctype.module]) {
								modules[doctype.module].push(doctype);
							}
						});

						// Render modules and DocTypes
						Object.keys(modules).forEach(function(module_name) {
							var doctypes = modules[module_name];
							if (doctypes.length > 0) {
								var module_completion = me.get_module_completion_status(app.name, module_name);
								app_html += `
									<div class="tree-node module-node" data-type="module" data-name="${module_name}" data-app="${app.name}">
										<div class="node-header">
											<input type="checkbox" class="completion-checkbox module-checkbox" data-type="module" data-name="${module_name}" data-app="${app.name}" ${module_completion ? 'checked' : ''}>
											<i class="fa fa-folder-o tree-icon"></i>
											<span class="node-title">${module_name}</span>
											<span class="node-count">(${doctypes.length})</span>
										</div>
										<div class="node-children" style="display: none;">
								`;

								doctypes.forEach(function(doctype) {
									var icon = me.get_doctype_icon(doctype);
									var doctype_completion = me.get_doctype_completion_status(doctype.name);
									app_html += `
										<div class="tree-node doctype-node" data-type="doctype" data-name="${doctype.name}" data-module="${module_name}" data-app="${app.name}">
											<div class="node-header">
												<input type="checkbox" class="completion-checkbox doctype-checkbox" data-type="doctype" data-name="${doctype.name}" data-module="${module_name}" data-app="${app.name}" ${doctype_completion ? 'checked' : ''}>
												<i class="${icon} tree-icon"></i>
												<span class="node-title">${doctype.name}</span>
												<span class="node-count">(${doctype.doc_count})</span>
											</div>
										</div>
									`;
								});

								app_html += '</div></div>';
							}
						});
					}

					app_html += '</div></div>';
					all_tree_html += app_html;
					completed_apps++;

					// When all apps are loaded, update the tree
					if (completed_apps === total_apps) {
						me.container.find('#roadmap-tree').html(all_tree_html);
						me.setup_tree_interactions();
					}
				}
			});
		});
	},

	setup_tree_interactions: function() {
		var me = this;

		// Setup completion handlers
		this.setup_completion_handlers();

		// Node click handlers
		this.container.find('.node-header').on('click', function(e) {
			e.stopPropagation();

			// Don't trigger if clicking on checkbox
			if ($(e.target).hasClass('completion-checkbox')) {
				return;
			}

			var node = $(this).parent();
			var type = node.data('type');
			var name = node.data('name');

			if (type === 'doctype') {
				// Get app and module context from parent nodes
				var module_node = node.closest('.tree-node[data-type="module"]');
				var app_node = node.closest('.tree-node[data-type="app"]');

				var module_name = module_node.length ? module_node.data('name') : null;
				var app_name = app_node.length ? app_node.data('name') : null;

				me.select_doctype(name, app_name, module_name);
			} else if (type === 'module') {
				var app_name = node.data('app');

				// Check if double-click for overview
				var clickCount = $(this).data('clickCount') || 0;
				clickCount++;
				$(this).data('clickCount', clickCount);

				setTimeout(() => {
					var currentCount = $(this).data('clickCount');
					if (currentCount === 1) {
						// Single click - toggle expansion
						var children = node.find('> .node-children');
						var icon = $(this).find('.tree-icon');

						if (children.is(':visible')) {
							children.hide();
							icon.removeClass('fa-folder-open-o').addClass('fa-folder-o');
						} else {
							children.show();
							icon.removeClass('fa-folder-o').addClass('fa-folder-open-o');
						}
					} else if (currentCount === 2) {
						// Double click - show overview
						me.show_module_overview(app_name, name);
					}
					$(this).data('clickCount', 0);
				}, 300);
			} else if (type === 'app') {
				// Check if double-click for overview
				var clickCount = $(this).data('clickCount') || 0;
				clickCount++;
				$(this).data('clickCount', clickCount);

				setTimeout(() => {
					var currentCount = $(this).data('clickCount');
					if (currentCount === 1) {
						// Single click - toggle expansion
						var children = node.find('> .node-children');
						var icon = $(this).find('.tree-icon');

						if (children.is(':visible')) {
							children.hide();
							icon.removeClass('fa-folder-open').addClass('fa-folder');
						} else {
							children.show();
							icon.removeClass('fa-folder').addClass('fa-folder-open');
						}
					} else if (currentCount === 2) {
						// Double click - show overview
						me.show_app_overview(name);
					}
					$(this).data('clickCount', 0);
				}, 300);
			}
		});
	},

	get_doctype_icon: function(doctype) {
		if (doctype.custom) return 'fa fa-file-code-o text-danger';
		if (doctype.istable) return 'fa fa-table text-warning';
		if (doctype.issingle) return 'fa fa-file-o text-info';
		return 'fa fa-file-text-o text-primary';
	},

	select_doctype_from_search: function(doctype_name) {
		var me = this;

		// First get doctype details to find app and module
		frappe.call({
			method: 'knowlan.api.v1.metadata.get_doctype_details',
			args: { doctype_name: doctype_name },
			callback: function(r) {
				if (r.message && r.message.doctype) {
					var doctype_data = r.message.doctype;
					var module_name = doctype_data.module;

					// Get app name for this module
					frappe.call({
						method: 'frappe.client.get_value',
						args: {
							doctype: 'Module Def',
							fieldname: 'app_name',
							filters: { name: module_name }
						},
						callback: function(app_result) {
							if (app_result.message && app_result.message.app_name) {
								var app_name = app_result.message.app_name;

								// Expand the tree to show the path
								me.expand_tree_to_doctype(app_name, module_name, doctype_name);

								// Select the doctype
								me.select_doctype(doctype_name, app_name, module_name);
							}
						}
					});
				}
			}
		});
	},

	select_doctype: function(doctype_name, app_name, module_name) {
		var me = this;

		// Update breadcrumb with proper path if available
		if (app_name && module_name) {
			this.update_breadcrumb([app_name, module_name, doctype_name], {
				app_name: app_name,
				module_name: module_name,
				doctype_name: doctype_name
			});
		} else {
			this.update_breadcrumb(['Apps', 'DocType', doctype_name]);
		}

		// Load DocType details
		frappe.call({
			method: 'knowlan.api.v1.metadata.get_doctype_details',
			args: { doctype_name: doctype_name },
			callback: function(r) {
				if (r.message) {
					me.current_doctype = r.message;
					me.render_doctype_overview(r.message);
					me.render_doctype_fields(r.message);
					me.load_doctype_code(doctype_name);
					me.load_doctype_notes(doctype_name);

					// Show tabs now that a doctype is selected
					me.show_tabs_for_selection('doctype');

					// Show the Open DocType button only for viewable doctypes
					if (me.is_doctype_viewable(r.message.doctype)) {
						me.container.find('#open-doctype').show();
					} else {
						me.container.find('#open-doctype').hide();
					}
				}
			}
		});

		// Highlight selected node in tree (use the renamed function)
		this.highlight_doctype_in_tree(doctype_name);
	},

	update_breadcrumb: function(path, context_data) {
		var me = this;
		var breadcrumb_html = '';

		path.forEach(function(item, index) {
			if (index === path.length - 1) {
				// Active (current) breadcrumb item
				breadcrumb_html += '<li class="breadcrumb-item active" aria-current="page">' + item + '</li>';
			} else {
				// Clickable breadcrumb items for navigation
				var data_attrs = '';
				if (context_data) {
					if (index === 0 && context_data.app_name) {
						data_attrs = 'data-type="app" data-name="' + context_data.app_name + '"';
					} else if (index === 1 && context_data.module_name) {
						data_attrs = 'data-type="module" data-name="' + context_data.module_name + '" data-app="' + context_data.app_name + '"';
					}
				}

				breadcrumb_html += '<li class="breadcrumb-item">' +
					'<a href="#" class="breadcrumb-link" data-path-index="' + index + '" ' + data_attrs + '>' +
					item + '</a></li>';
			}
		});

		this.container.find('#classroom-breadcrumb').html(breadcrumb_html);
		this.setup_breadcrumb_handlers();
	},

	render_doctype_overview: function(doctype_data) {
		var doctype = doctype_data.doctype;
		var stats = doctype_data.statistics;
		
		var overview_html = '<div class="doctype-overview">' +
			'<div class="overview-header">' +
			'<h3>' + doctype.name + '</h3>' +
			'<div class="description-section" id="description-section">' +
			this.render_description_content(doctype) +
			'</div>' +
			'<div class="documentation-section" id="documentation-section">' +
			this.render_documentation_content(doctype) +
			'</div>' +
			'</div>' +
			'<div class="overview-stats">' +
			'<div class="row">' +
			'<div class="col-md-3">' +
			'<div class="stat-card clickable-stat" data-action="open-list" style="cursor: pointer;">' +
			'<div class="stat-value">' + (doctype.doc_count || 0) + '</div>' +
			'<div class="stat-label">Documents</div>' +
			'</div>' +
			'</div>' +
			'<div class="col-md-3">' +
			'<div class="stat-card">' +
			'<div class="stat-value">' + (stats.total_fields || 0) + '</div>' +
			'<div class="stat-label">Fields</div>' +
			'</div>' +
			'</div>' +
			'<div class="col-md-3">' +
			'<div class="stat-card">' +
			'<div class="stat-value">' + (stats.link_fields || 0) + '</div>' +
			'<div class="stat-label">Link Fields</div>' +
			'</div>' +
			'</div>' +
			'<div class="col-md-3">' +
			'<div class="stat-card">' +
			'<div class="stat-value">' + (stats.table_fields || 0) + '</div>' +
			'<div class="stat-label">Table Fields</div>' +
			'</div>' +
			'</div>' +
			'</div>' +
			'</div>' +
			'<div class="overview-properties">' +
			'<h5>Properties</h5>' +
			'<div class="properties-grid">' +
			'<div class="property-item">' +
			'<strong>Module:</strong> ' + (doctype.module || 'N/A') +
			'</div>' +
			'<div class="property-item">' +
			'<strong>Custom:</strong> ' + (doctype.custom ? 'Yes' : 'No') +
			'</div>' +
			'<div class="property-item">' +
			'<strong>Is Table:</strong> ' + (doctype.istable ? 'Yes' : 'No') +
			'</div>' +
			'<div class="property-item">' +
			'<strong>Is Single:</strong> ' + (doctype.issingle ? 'Yes' : 'No') +
			'</div>' +
			'<div class="property-item">' +
			'<strong>Submittable:</strong> ' + (doctype.is_submittable ? 'Yes' : 'No') +
			'</div>' +
			'<div class="property-item">' +
			'<strong>Tree Structure:</strong> ' + (doctype.is_tree ? 'Yes' : 'No') +
			'</div>' +
			'</div>' +
			'</div>' +
			'</div>';
		
		this.container.find('#overview-panel').html(overview_html);

		// Setup personal description handlers
		this.setup_description_handlers();

		// Setup documentation handlers
		this.setup_documentation_handlers();

		// Setup clickable stat cards
		this.setup_stat_card_handlers();
	},

	render_doctype_fields: function(doctype_data) {
		var fields = doctype_data.fields;

		var fields_html = `
			<div class="fields-list">
				<div class="fields-header">
					<h5 id="fields-count-header">Fields: ${fields.length}</h5>
					<div class="progress-controls">
						<label class="checkbox-label">
							<input type="checkbox" id="include-code-tracking"> Include Code Files
						</label>
						<div class="progress-summary" id="progress-summary">
							<span class="completed-count">0</span> / <span class="total-count">${fields.length}</span> items completed
							(<span class="completion-percentage">0%</span>)
						</div>
					</div>
				</div>
				<div class="fields-table">
					<table class="table table-striped">
						<thead>
							<tr class="filter-row">
								<th style="width: 50px;">
									<input type="checkbox" id="select-all-fields" title="Select All">
								</th>
								<th>
									<input type="text" class="form-control form-control-sm mb-1 filter-search" placeholder="Search..." data-target="filter-fieldname">
									<select class="form-control form-control-sm column-filter" id="filter-fieldname">
										<option value="">All Field Names</option>
									</select>
								</th>
								<th>
									<input type="text" class="form-control form-control-sm mb-1 filter-search" placeholder="Search..." data-target="filter-label">
									<select class="form-control form-control-sm column-filter" id="filter-label">
										<option value="">All Labels</option>
									</select>
								</th>
								<th>
									<input type="text" class="form-control form-control-sm mb-1 filter-search" placeholder="Search..." data-target="filter-fieldtype">
									<select class="form-control form-control-sm column-filter" id="filter-fieldtype">
										<option value="">All Types</option>
									</select>
								</th>
								<th>
									<input type="text" class="form-control form-control-sm mb-1 filter-search" placeholder="Search..." data-target="filter-options">
									<select class="form-control form-control-sm column-filter" id="filter-options">
										<option value="">All Options</option>
									</select>
								</th>
								<th>
									<input type="text" class="form-control form-control-sm mb-1 filter-search" placeholder="Search..." data-target="filter-properties">
									<select class="form-control form-control-sm column-filter" id="filter-properties">
										<option value="">All Properties</option>
									</select>
								</th>
							</tr>
							<tr>
								<th>✓</th>
								<th>Field Name</th>
								<th>Label</th>
								<th>Type</th>
								<th>Options</th>
								<th>Properties</th>
							</tr>
						</thead>
						<tbody>
		`;
		
		var me = this;
		fields.forEach(function(field) {
			var properties = [];
			if (field.reqd) properties.push('<span class="property-badge property-required">Required</span>');
			if (field.hidden) properties.push('<span class="property-badge property-hidden">Hidden</span>');
			if (field.unique) properties.push('<span class="property-badge property-unique">Unique</span>');
			if (field.search_index) properties.push('<span class="property-badge property-indexed">Indexed</span>');
			if (field.read_only) properties.push('<span class="property-badge property-readonly">Read Only</span>');
			if (field.in_list_view) properties.push('<span class="property-badge property-listview">In List</span>');
			if (field.in_standard_filter) properties.push('<span class="property-badge property-filter">Filter</span>');

			// Check if field is completed
			var isCompleted = me.is_field_completed(field.fieldname);
			var checkedAttr = isCompleted ? 'checked' : '';

			fields_html += `
				<tr class="field-row ${field.hidden ? 'hidden-field' : ''} ${field.reqd ? 'required-field' : ''}" data-fieldname="${field.fieldname}">
					<td><input type="checkbox" class="field-completion-checkbox" data-fieldname="${field.fieldname}" ${checkedAttr}></td>
					<td><code class="field-name-clickable" style="cursor: pointer; color: #007bff;">${field.fieldname}</code></td>
					<td>${field.label || ''}</td>
					<td><span class="fieldtype-badge">${field.fieldtype}</span></td>
					<td>${field.options || ''}</td>
					<td class="properties-cell">${properties.join(' ')}</td>
				</tr>
			`;
		});
		
		fields_html += '</tbody></table></div></div>';

		this.container.find('#fields-panel').html(fields_html);

		// Populate column filters with unique values
		this.populate_column_filters(fields);

		// Setup field event handlers
		this.setup_field_handlers();

		// Setup progress tracking handlers
		this.setup_progress_handlers();

		// Update initial progress display
		this.update_progress_display();

		// Initialize code tracking preference
		this.initialize_code_tracking_preference();
	},

	setup_field_handlers: function() {
		var me = this;

		// Setup change handlers for filters
		this.container.find('.column-filter').on('change', function() {
			me.apply_column_filters();
		});

		// Setup search functionality for filter dropdowns
		this.container.find('.filter-search').on('input', function() {
			var search_term = $(this).val().toLowerCase();
			var target_select = $(this).data('target');
			var select_element = me.container.find('#' + target_select);

			// Clear current selection if search term doesn't match
			var current_value = select_element.val();
			var current_text = select_element.find('option:selected').text().toLowerCase();

			if (current_value && !current_text.includes(search_term)) {
				select_element.val('');
				me.apply_column_filters();
			}

			// Filter options based on search term
			var visible_options = 0;
			select_element.find('option').each(function() {
				var option_text = $(this).text().toLowerCase();
				var is_empty_option = $(this).val() === '';

				if (is_empty_option || option_text.includes(search_term)) {
					$(this).show();
					visible_options++;
				} else {
					$(this).hide();
				}
			});

			// If no search term, show all options
			if (!search_term) {
				select_element.find('option').show();
			}

			// Apply filters immediately after search
			me.apply_column_filters();
		});

		// Setup field name click handlers
		this.container.find('.field-name-clickable').on('click', function() {
			var fieldname = $(this).text();
			var row = $(this).closest('.field-row');
			me.show_field_selection_modal(fieldname, row);
		});
	},

	populate_column_filters: function(fields) {
		var me = this;

		// Extract unique values for each column
		var unique_fieldnames = [...new Set(fields.map(f => f.fieldname))].sort();
		var unique_labels = [...new Set(fields.map(f => f.label).filter(l => l))].sort();
		var unique_types = [...new Set(fields.map(f => f.fieldtype))].sort();
		var unique_options = [...new Set(fields.map(f => f.options).filter(o => o))].sort();

		// Get unique properties
		var all_properties = [];
		fields.forEach(function(field) {
			if (field.reqd) all_properties.push('Required');
			if (field.hidden) all_properties.push('Hidden');
			if (field.unique) all_properties.push('Unique');
			if (field.search_index) all_properties.push('Indexed');
			if (field.read_only) all_properties.push('Read Only');
			if (field.in_list_view) all_properties.push('In List');
			if (field.in_standard_filter) all_properties.push('Filter');
		});
		var unique_properties = [...new Set(all_properties)].sort();

		// Populate fieldname filter
		var fieldname_select = this.container.find('#filter-fieldname');
		unique_fieldnames.forEach(function(name) {
			fieldname_select.append(`<option value="${name}">${name}</option>`);
		});

		// Populate label filter
		var label_select = this.container.find('#filter-label');
		unique_labels.forEach(function(label) {
			label_select.append(`<option value="${label}">${label}</option>`);
		});

		// Populate type filter
		var type_select = this.container.find('#filter-fieldtype');
		unique_types.forEach(function(type) {
			type_select.append(`<option value="${type}">${type}</option>`);
		});

		// Populate options filter
		var options_select = this.container.find('#filter-options');
		unique_options.forEach(function(option) {
			options_select.append(`<option value="${option}">${option}</option>`);
		});

		// Populate properties filter
		var properties_select = this.container.find('#filter-properties');
		unique_properties.forEach(function(prop) {
			properties_select.append(`<option value="${prop}">${prop}</option>`);
		});

		// Field handlers are now set up in setup_field_handlers() after fields are rendered
	},

	apply_column_filters: function() {
		var me = this;

		// Get filter values
		var fieldname_filter = this.container.find('#filter-fieldname').val();
		var label_filter = this.container.find('#filter-label').val();
		var type_filter = this.container.find('#filter-fieldtype').val();
		var options_filter = this.container.find('#filter-options').val();
		var properties_filter = this.container.find('#filter-properties').val();

		var visible_count = 0;

		this.container.find('.field-row').each(function() {
			var row = $(this);
			var show = true;

			// Get row data (adjusted for checkbox column)
			var fieldname = row.find('td:nth-child(2) code').text();
			var label = row.find('td:nth-child(3)').text();
			var type = row.find('td:nth-child(4) .fieldtype-badge').text();
			var options = row.find('td:nth-child(5)').text();

			// Get properties from badges
			var properties = [];
			row.find('td:nth-child(6) .property-badge').each(function() {
				properties.push($(this).text());
			});

			// Apply filters
			if (fieldname_filter && fieldname !== fieldname_filter) {
				show = false;
			}

			if (label_filter && label !== label_filter) {
				show = false;
			}

			if (type_filter && type !== type_filter) {
				show = false;
			}

			if (options_filter && options !== options_filter) {
				show = false;
			}

			if (properties_filter && !properties.includes(properties_filter)) {
				show = false;
			}

			if (show) {
				row.show();
				visible_count++;
			} else {
				row.hide();
			}
		});

		// Update the field count header
		this.container.find('#fields-count-header').text('Fields: ' + visible_count);
	},

	filter_fields: function() {
		// This method is now replaced by apply_column_filters
		this.apply_column_filters();
	},

	load_doctype_code: function(doctype_name) {
		var me = this;

		// Load actual code from the backend
		frappe.call({
			method: 'knowlan.api.v1.utils.search_codebase',
			args: {
				search_term: doctype_name,
				file_types: ['py'],
				search_type: 'doctype_controller'
			},
			callback: function(r) {
				if (r.message && r.message.results && r.message.results.length > 0) {
					me.show_file_selection(r.message.results, doctype_name);
				} else {
					// Fallback to template if no real code found
					var code_content = me.get_doctype_template(doctype_name);
					if (me.monaco_editor) {
						me.monaco_editor.setValue(code_content);
					}
				}
			},
			error: function() {
				// Fallback to template on error
				var template_code = me.get_doctype_template(doctype_name);
				if (me.monaco_editor) {
					me.monaco_editor.setValue(template_code);
				}
			}
		});
	},

	restore_original_code_panel: function() {
		// Restore the original code panel HTML structure
		var original_html = `
			<div class="code-viewer-container">
				<div class="code-viewer-header">
					<div class="file-tabs" id="file-tabs">
						<!-- File tabs will be added here -->
					</div>
					<div class="code-controls">
						<button class="btn btn-default btn-sm" id="format-code">
							<i class="fa fa-magic"></i> Format
						</button>
						<button class="btn btn-default btn-sm" id="copy-code">
							<i class="fa fa-copy"></i> Copy
						</button>
					</div>
				</div>
				<div class="code-editor" id="monaco-container">
					<!-- Monaco Editor will be initialized here -->
				</div>
			</div>
		`;

		var code_panel = this.container.find('#code-panel');
		code_panel.html(original_html);

		// Re-initialize Monaco editor if needed
		this.init_monaco_editor();
	},

	show_file_selection: function(files, doctype_name) {
		var me = this;

		// Create file selection interface
		var file_selection_html = `
			<div class="file-selection-container">
				<div class="file-selection-header">
					<h6><i class="fa fa-file-code-o"></i> Select File to View (${files.length} files found)</h6>
				</div>
				<div class="file-list">
		`;

		files.forEach(function(file, index) {
			var file_icon = me.get_file_icon(file.file_path);
			var file_size = me.format_file_size(file.size);
			var is_completed = me.is_code_file_completed(doctype_name, file.file_path);

			file_selection_html += `
				<div class="file-item" data-index="${index}">
					<div class="file-info">
						<input type="checkbox" class="code-file-checkbox" data-file-path="${file.file_path}" ${is_completed ? 'checked' : ''}>
						<i class="${file_icon}"></i>
						<span class="file-name">${file.file_path}</span>
						<span class="file-size">${file_size}</span>
					</div>
					<button class="btn btn-xs btn-primary view-file-btn" data-index="${index}">
						<i class="fa fa-eye"></i> View
					</button>
				</div>
			`;
		});

		file_selection_html += `
				</div>
				<div class="file-selection-footer">
					<button class="btn btn-sm btn-primary" id="show-references">
						<i class="fa fa-search"></i> Show References
					</button>
					<button class="btn btn-sm btn-default" id="close-file-selection">
						<i class="fa fa-times"></i> Close
					</button>
				</div>
			</div>
		`;

		// Show file selection in code panel
		var code_panel = this.container.find('#code-panel');
		code_panel.html(file_selection_html);

		// Setup event handlers
		code_panel.find('.view-file-btn').on('click', function() {
			var index = $(this).data('index');
			var selected_file = files[index];
			me.display_file_content(selected_file, files, doctype_name);
		});

		code_panel.find('#show-references').on('click', function() {
			me.show_doctype_references(doctype_name, files);
		});

		code_panel.find('#close-file-selection').on('click', function() {
			// Restore original code panel
			me.restore_original_code_panel();
			var code_content = me.get_doctype_template(doctype_name);
			if (me.monaco_editor) {
				me.monaco_editor.setValue(code_content);
			}
		});

		// Setup code file completion handlers
		code_panel.find('.code-file-checkbox').on('change', function() {
			var file_path = $(this).data('file-path');
			var completed = $(this).is(':checked');
			me.set_code_file_completion(doctype_name, file_path, completed);

			frappe.show_alert({
				message: 'Code file "' + file_path + '" marked as ' + (completed ? 'completed' : 'incomplete'),
				indicator: completed ? 'green' : 'orange'
			}, 2);
		});
	},

	display_file_content: function(file, all_files, doctype_name) {
		var me = this;

		// Create file viewer interface
		var file_viewer_html = `
			<div class="file-viewer-container">
				<div class="file-viewer-header">
					<div class="file-info">
						<i class="${me.get_file_icon(file.file_path)}"></i>
						<span class="file-path">${file.file_path}</span>
						<span class="file-size">(${me.format_file_size(file.size)})</span>
					</div>
					<div class="file-actions">
						<button class="btn btn-xs btn-default" id="back-to-files">
							<i class="fa fa-arrow-left"></i> Back to Files
						</button>
						<button class="btn btn-xs btn-primary" id="copy-file-content">
							<i class="fa fa-copy"></i> Copy
						</button>
						<button class="btn btn-xs btn-success" id="download-file">
							<i class="fa fa-download"></i> Download
						</button>
					</div>
				</div>
				<div class="file-content-container">
					<div id="file-code-editor" style="height: 500px; border: 1px solid #ddd;"></div>
				</div>
			</div>
		`;

		var code_panel = this.container.find('#code-panel');
		code_panel.html(file_viewer_html);

		// Initialize Monaco editor for file content
		require(['vs/editor/editor.main'], function() {
			me.file_monaco_editor = monaco.editor.create(code_panel.find('#file-code-editor')[0], {
				value: file.content,
				language: me.get_monaco_language(file.file_path),
				theme: 'vs',
				readOnly: true,
				minimap: { enabled: false },
				scrollBeyondLastLine: false,
				automaticLayout: true
			});
		});

		// Setup event handlers
		code_panel.find('#back-to-files').on('click', function() {
			if (me.file_monaco_editor) {
				me.file_monaco_editor.dispose();
				me.file_monaco_editor = null;
			}
			me.show_file_selection(all_files, doctype_name);
		});

		code_panel.find('#copy-file-content').on('click', function() {
			navigator.clipboard.writeText(file.content).then(function() {
				frappe.show_alert({message: __('File content copied to clipboard'), indicator: 'green'});
			});
		});

		code_panel.find('#download-file').on('click', function() {
			me.download_file_content(file);
		});
	},

	get_file_icon: function(file_path) {
		var extension = file_path.split('.').pop().toLowerCase();
		var icons = {
			'py': 'fa fa-file-code-o text-primary',
			'js': 'fa fa-file-code-o text-warning',
			'html': 'fa fa-file-code-o text-danger',
			'css': 'fa fa-file-code-o text-info',
			'json': 'fa fa-file-text-o text-success',
			'md': 'fa fa-file-text-o text-muted'
		};
		return icons[extension] || 'fa fa-file-o text-muted';
	},

	get_monaco_language: function(file_path) {
		var extension = file_path.split('.').pop().toLowerCase();
		var languages = {
			'py': 'python',
			'js': 'javascript',
			'html': 'html',
			'css': 'css',
			'json': 'json',
			'md': 'markdown'
		};
		return languages[extension] || 'text';
	},

	format_file_size: function(bytes) {
		if (bytes === 0) return '0 Bytes';
		var k = 1024;
		var sizes = ['Bytes', 'KB', 'MB', 'GB'];
		var i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	},

	download_file_content: function(file) {
		var blob = new Blob([file.content], { type: 'text/plain' });
		var url = window.URL.createObjectURL(blob);
		var a = document.createElement('a');
		a.href = url;
		a.download = file.file_path.split('/').pop();
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		window.URL.revokeObjectURL(url);
	},

	get_doctype_template: function(doctype_name) {
		return `# ${doctype_name} DocType Controller
# Generated template - actual controller may differ

import frappe
from frappe.model.document import Document

class ${doctype_name.replace(/\s+/g, '')}(Document):
    def validate(self):
        # Add validation logic here
        pass

    def before_save(self):
        # Add before save logic here
        pass

    def after_insert(self):
        # Add after insert logic here
        pass

    def on_update(self):
        # Add on update logic here
        pass

    def on_submit(self):
        # Add on submit logic here
        pass

    def on_cancel(self):
        # Add on cancel logic here
        pass
`;
	},

	load_doctype_notes: function(doctype_name) {
		var me = this;

		// Create simplified notes panel if not already created
		if (this.container.find('#notes-panel').is(':empty')) {
			var notes_html = `
				<div class="notes-workspace-simple">
					<div class="notes-sidebar">
						<div class="notes-sidebar-header">
							<h6><i class="fa fa-book"></i> Knowledge Vault</h6>
						</div>
						<div class="notes-sidebar-content">
							<div id="pages-list" class="pages-list"></div>
							<button class="add-page-btn" id="add-page">
								<i class="fa fa-plus"></i> New Page
							</button>
						</div>
					</div>
					<div class="notes-main">
						<div class="notes-main-header">
							<h3 class="notes-title" id="current-page-title">Notes</h3>
							<div class="notes-actions">
								<button class="btn btn-sm btn-secondary" id="reload-toc" title="Reload Table of Contents">
									<i class="fa fa-refresh"></i> TOC
								</button>
							</div>
						</div>
						<div class="notes-toolbar">
							<div class="formatting-buttons">
								<button class="btn" data-format="bold" title="Bold (Ctrl+B)"><i class="fa fa-bold"></i></button>
								<button class="btn" data-format="italic" title="Italic (Ctrl+I)"><i class="fa fa-italic"></i></button>
								<button class="btn" data-format="heading" title="Heading"><i class="fa fa-header"></i></button>
								<button class="btn" data-format="list" title="List"><i class="fa fa-list-ul"></i></button>
								<button class="btn" data-format="code" title="Inline Code"><i class="fa fa-code"></i></button>
								<button class="btn" data-format="codeblock" title="Code Block"><i class="fa fa-file-code-o"></i></button>
								<button class="btn" data-format="link" title="Link"><i class="fa fa-link"></i></button>
								<button class="btn" data-format="table" title="Table"><i class="fa fa-table"></i></button>
							</div>
						</div>
						<div class="notes-content">
							<div class="editor-container">
								<div class="markdown-editor" id="markdown-editor" contenteditable="true"></div>
							</div>
							<div class="toc-sidebar">
								<div class="toc-header">
									<h6>Table of Contents</h6>
								</div>
								<div class="toc-list" id="toc-list"></div>
							</div>
						</div>
					</div>
				</div>
			`;

			this.container.find('#notes-panel').html(notes_html);
			this.setup_notes_handlers();
		}

		// Initialize pages system
		this.current_page = 'main';
		this.pages = {};

		// Load main notes from localStorage or server
		var notes = localStorage.getItem(`knowlan_notes_${doctype_name}`) || '# ' + doctype_name + ' Notes\n\nStart writing your notes here...';
		this.container.find('#markdown-editor').html(this.render_markdown_content(notes));

		// Store raw content for editing
		this.container.find('#markdown-editor').data('raw-content', notes);

		// Render pages list
		this.render_pages_list();

		// Generate TOC for existing content
		this.generate_table_of_contents();
	},

	show_panel: function(panel_name) {
		this.container.find('.content-panel').hide();
		this.container.find(`#${panel_name}-panel`).show();

		// Resize Monaco Editor if code panel is shown
		if (panel_name === 'code' && this.monaco_editor) {
			setTimeout(() => {
				this.monaco_editor.layout();
			}, 100);
		}

		// Load links if links panel is shown
		if (panel_name === 'links' && this.current_doctype) {
			this.load_doctype_links(this.current_doctype.doctype.name);
		}
	},

	set_active_view: function(button) {
		this.container.find('.main-controls .btn').removeClass('btn-primary').addClass('btn-default');
		button.removeClass('btn-default').addClass('btn-primary');
	},

	expand_tree_to_doctype: function(app_name, module_name, doctype_name) {
		var me = this;

		// Find and expand app node
		var app_node = this.container.find('.tree-node[data-type="app"][data-name="' + app_name + '"]');
		if (app_node.length) {
			var app_children = app_node.find('> .node-children');
			var app_icon = app_node.find('> .node-header .tree-icon');

			if (!app_children.is(':visible')) {
				app_children.show();
				app_icon.removeClass('fa-folder').addClass('fa-folder-open');
			}

			// Find and expand module node
			var module_node = app_node.find('.tree-node[data-type="module"][data-name="' + module_name + '"]');
			if (module_node.length) {
				var module_children = module_node.find('> .node-children');
				var module_icon = module_node.find('> .node-header .tree-icon');

				if (!module_children.is(':visible')) {
					module_children.show();
					module_icon.removeClass('fa-folder-o').addClass('fa-folder-open-o');
				}
			}
		}
	},

	highlight_doctype_in_tree: function(doctype_name) {
		// Find and highlight the doctype in the tree (renamed to avoid recursion)
		var me = this;
		var tree_nodes = this.container.find('.tree-node');

		tree_nodes.each(function() {
			var node = $(this);
			var node_title = node.find('.node-title').text().trim();

			if (node_title === doctype_name) {
				// Clear previous selections
				tree_nodes.removeClass('selected');

				// Select this node
				node.addClass('selected');

				// Scroll to the node
				node[0].scrollIntoView({ behavior: 'smooth', block: 'center' });

				return false; // Break the loop
			}
		});
	},

	filter_roadmap: function(query) {
		if (!query) {
			this.container.find('.tree-node').show();
			return;
		}
		
		query = query.toLowerCase();
		this.container.find('.doctype-node').each(function() {
			var name = $(this).data('name').toLowerCase();
			if (name.includes(query)) {
				$(this).show();
				$(this).parents('.tree-node').show();
			} else {
				$(this).hide();
			}
		});
	},

	format_code: function() {
		if (this.monaco_editor) {
			this.monaco_editor.getAction('editor.action.formatDocument').run();
		}
	},

	copy_code: function() {
		if (this.monaco_editor) {
			var code = this.monaco_editor.getValue();
			navigator.clipboard.writeText(code).then(function() {
				frappe.show_alert({message: __('Code copied to clipboard'), indicator: 'green'});
			});
		}
	},

	save_notes: function() {
		var notes = this.container.find('#notes-textarea').val();
		if (this.current_doctype) {
			var doctype_name = this.current_doctype.doctype.name;
			localStorage.setItem(`knowlan_notes_${doctype_name}`, notes);
			frappe.show_alert({message: __('Notes saved'), indicator: 'green'});
		}
	},

	export_notes: function() {
		var notes = this.container.find('#notes-textarea').val();
		if (notes && this.current_doctype) {
			var doctype_name = this.current_doctype.doctype.name;
			var blob = new Blob([notes], { type: 'text/markdown' });
			var url = URL.createObjectURL(blob);
			var link = document.createElement('a');
			link.download = `${doctype_name}_notes.md`;
			link.href = url;
			link.click();
		}
	},

	is_doctype_viewable: function(doctype) {
		// Check if doctype has a viewable interface
		if (doctype.issingle) return false;  // Single doctypes don't have list view
		if (doctype.istable) return false;   // Table doctypes are child tables
		if (doctype.name.startsWith('__')) return false; // System doctypes

		// Additional checks for non-viewable doctypes
		var non_viewable_types = [
			'DocType', 'DocField', 'DocPerm', 'Property Setter',
			'Custom Field', 'Custom Script', 'Server Script',
			'Client Script', 'Print Format', 'Report'
		];

		return !non_viewable_types.includes(doctype.name);
	},

	is_doctype_customizable: function(doctype) {
		// Check if doctype can be customized (non-core doctypes)
		return doctype.custom || !doctype.module ||
			   !['Core', 'Desk', 'Email', 'Printing', 'Website'].includes(doctype.module);
	},

	show_doctype_navigation_modal: function(doctype_data) {
		var me = this;
		var doctype = doctype_data.doctype;
		var doctype_name = doctype.name;

		// Create modal content
		var modal_content = '<div class="doctype-navigation-modal">' +
			'<h4>Open ' + doctype_name + '</h4>' +
			'<p>Choose how you want to open this DocType:</p>' +
			'<div class="navigation-options">' +
			'<button class="btn btn-primary btn-block mb-2" data-action="list">' +
			'<i class="fa fa-list"></i> List View</button>';

		// Add Edit option only for customizable doctypes
		if (this.is_doctype_customizable(doctype)) {
			modal_content += '<button class="btn btn-secondary btn-block mb-2" data-action="edit">' +
				'<i class="fa fa-edit"></i> Edit DocType</button>';
		}

		modal_content += '<button class="btn btn-info btn-block mb-2" data-action="customize">' +
			'<i class="fa fa-cog"></i> Customize</button>' +
			'</div></div>';

		// Create and show modal
		var modal = new frappe.ui.Dialog({
			title: 'DocType Navigation',
			fields: [{
				fieldtype: 'HTML',
				fieldname: 'navigation_content',
				options: modal_content
			}],
			primary_action_label: 'Cancel'
		});

		modal.show();

		// Setup button handlers
		modal.$wrapper.find('[data-action]').on('click', function() {
			var action = $(this).data('action');
			me.handle_doctype_navigation(doctype_name, action);
			modal.hide();
		});
	},

	handle_doctype_navigation: function(doctype_name, action) {
		switch(action) {
			case 'list':
				frappe.set_route('List', doctype_name);
				break;
			case 'edit':
				frappe.set_route('Form', 'DocType', doctype_name);
				break;
			case 'customize':
				frappe.set_route('Form', 'Customize Form', doctype_name);
				break;
		}

		frappe.show_alert({
			message: 'Opening ' + doctype_name,
			indicator: 'blue'
		}, 2);
	},

	open_doctype_form: function(doctype_data) {
		if (doctype_data && doctype_data.doctype) {
			var doctype = doctype_data.doctype;

			// Check if doctype is viewable
			if (!this.is_doctype_viewable(doctype)) {
				frappe.show_alert({
					message: 'This DocType cannot be opened directly',
					indicator: 'orange'
				}, 3);
				return;
			}

			// Show navigation modal for viewable doctypes
			this.show_doctype_navigation_modal(doctype_data);
		}
	},

	apply_markdown_format: function(format) {
		var textarea = this.container.find('#notes-textarea')[0];
		var start = textarea.selectionStart;
		var end = textarea.selectionEnd;
		var selectedText = textarea.value.substring(start, end);
		var replacement = '';
		var selectStart = start;
		var selectEnd = start;

		switch(format) {
			case 'bold':
				if (selectedText) {
					replacement = `**${selectedText}**`;
					selectEnd = start + replacement.length;
				} else {
					replacement = '**enter bold text**';
					selectStart = start + 2;
					selectEnd = start + replacement.length - 2;
				}
				break;
			case 'italic':
				if (selectedText) {
					replacement = `*${selectedText}*`;
					selectEnd = start + replacement.length;
				} else {
					replacement = '*enter italic text*';
					selectStart = start + 1;
					selectEnd = start + replacement.length - 1;
				}
				break;
			case 'underline':
				if (selectedText) {
					replacement = `<u>${selectedText}</u>`;
					selectEnd = start + replacement.length;
				} else {
					replacement = '<u>enter underlined text</u>';
					selectStart = start + 3;
					selectEnd = start + replacement.length - 4;
				}
				break;
			case 'highlight':
				if (selectedText) {
					replacement = `==${selectedText}==`;
					selectEnd = start + replacement.length;
				} else {
					replacement = '==enter highlighted text==';
					selectStart = start + 2;
					selectEnd = start + replacement.length - 2;
				}
				break;
			case 'heading':
				if (selectedText) {
					var lines = selectedText.split('\n');
					replacement = lines.map(line => line.startsWith('#') ? '#' + line : '# ' + line).join('\n');
					selectEnd = start + replacement.length;
				} else {
					replacement = '# Enter heading text';
					selectStart = start + 2;
					selectEnd = start + replacement.length;
				}
				break;
			case 'code':
				if (selectedText) {
					replacement = `\`${selectedText}\``;
					selectEnd = start + replacement.length;
				} else {
					replacement = '`enter code`';
					selectStart = start + 1;
					selectEnd = start + replacement.length - 1;
				}
				break;
			case 'codeblock':
				if (selectedText) {
					replacement = `\`\`\`\n${selectedText}\n\`\`\``;
					selectEnd = start + replacement.length;
				} else {
					replacement = '```\nenter code block\n```';
					selectStart = start + 4;
					selectEnd = start + replacement.length - 4;
				}
				break;
			case 'link':
				if (selectedText) {
					replacement = `[${selectedText}](enter-url)`;
					selectStart = start + selectedText.length + 3;
					selectEnd = start + replacement.length - 1;
				} else {
					replacement = '[enter link text](enter-url)';
					selectStart = start + 1;
					selectEnd = start + 16;
				}
				break;
			case 'list':
				if (selectedText) {
					var lines = selectedText.split('\n');
					replacement = lines.map(line => line.trim() ? '- ' + line : line).join('\n');
					selectEnd = start + replacement.length;
				} else {
					replacement = '- Enter list item';
					selectStart = start + 2;
					selectEnd = start + replacement.length;
				}
				break;
			case 'table':
				replacement = '| Header 1 | Header 2 | Header 3 |\n|----------|----------|----------|\n| Cell 1   | Cell 2   | Cell 3   |';
				selectStart = start + 2;
				selectEnd = start + 10;
				break;
			case 'line':
				replacement = '\n---\n';
				selectEnd = start + replacement.length;
				break;
		}

		// Replace selected text
		textarea.value = textarea.value.substring(0, start) + replacement + textarea.value.substring(end);

		// Update cursor position and selection
		textarea.setSelectionRange(selectStart, selectEnd);
		textarea.focus();

		// Trigger TOC generation
		this.generate_table_of_contents();
	},

	toggle_find_replace: function() {
		var panel = this.container.find('#find-replace-panel');
		if (panel.is(':visible')) {
			panel.hide();
		} else {
			panel.show();
			this.container.find('#find-input').focus();
		}
	},

	find_next: function() {
		var textarea = this.container.find('#notes-textarea')[0];
		var findText = this.container.find('#find-input').val();
		if (!findText) return;

		var content = textarea.value;
		var currentPos = textarea.selectionEnd;
		var foundIndex = content.indexOf(findText, currentPos);

		if (foundIndex === -1) {
			// Search from beginning
			foundIndex = content.indexOf(findText, 0);
		}

		if (foundIndex !== -1) {
			textarea.setSelectionRange(foundIndex, foundIndex + findText.length);
			textarea.focus();
		}
	},

	replace_current: function() {
		var textarea = this.container.find('#notes-textarea')[0];
		var findText = this.container.find('#find-input').val();
		var replaceText = this.container.find('#replace-input').val();

		if (!findText) return;

		var start = textarea.selectionStart;
		var end = textarea.selectionEnd;
		var selectedText = textarea.value.substring(start, end);

		if (selectedText === findText) {
			textarea.value = textarea.value.substring(0, start) + replaceText + textarea.value.substring(end);
			textarea.setSelectionRange(start + replaceText.length, start + replaceText.length);
			this.generate_table_of_contents();
		}
	},

	replace_all: function() {
		var textarea = this.container.find('#notes-textarea')[0];
		var findText = this.container.find('#find-input').val();
		var replaceText = this.container.find('#replace-input').val();

		if (!findText) return;

		var newContent = textarea.value.replace(new RegExp(findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replaceText);
		textarea.value = newContent;
		this.generate_table_of_contents();
	},

	toggle_preview: function() {
		var editor = this.container.find('.notes-editor');
		var preview = this.container.find('#notes-preview');
		var button = this.container.find('#toggle-preview');

		if (preview.is(':visible')) {
			preview.hide();
			editor.show();
			button.html('<i class="fa fa-eye"></i> Preview');
		} else {
			this.render_markdown_preview();
			editor.hide();
			preview.show();
			button.html('<i class="fa fa-edit"></i> Edit');
		}
	},

	toggle_sidebar: function() {
		var sidebar = this.container.find('#notes-sidebar');
		var toggleBtn = this.container.find('#toggle-sidebar');

		if (sidebar.is(':visible')) {
			sidebar.hide();
			// Update main notes area to show TOC toggle button
			this.show_toc_toggle_button();
		} else {
			sidebar.show();
			this.generate_table_of_contents();
			// Hide the TOC toggle button since sidebar is visible
			this.hide_toc_toggle_button();
		}
	},

	show_toc_toggle_button: function() {
		var notesHeader = this.container.find('.notes-toolbar');
		if (notesHeader.find('#show-toc-btn').length === 0) {
			notesHeader.append('<button class="btn btn-sm btn-outline-info ml-2" id="show-toc-btn" title="Show Table of Contents">' +
				'<i class="fa fa-list"></i> TOC</button>');

			var me = this;
			this.container.find('#show-toc-btn').on('click', function() {
				me.toggle_sidebar();
			});
		}
	},

	hide_toc_toggle_button: function() {
		this.container.find('#show-toc-btn').remove();
	},

	render_markdown_preview: function() {
		var content = this.container.find('#notes-textarea').val();
		var preview = this.container.find('#notes-preview');

		// Simple markdown rendering (you might want to use a proper markdown library)
		var html = content
			.replace(/==(.*?)==/g, '<mark>$1</mark>')
			.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
			.replace(/\*(.*?)\*/g, '<em>$1</em>')
			.replace(/<u>(.*?)<\/u>/g, '<u>$1</u>')
			.replace(/^# (.*$)/gim, '<h1>$1</h1>')
			.replace(/^## (.*$)/gim, '<h2>$1</h2>')
			.replace(/^### (.*$)/gim, '<h3>$1</h3>')
			.replace(/^#### (.*$)/gim, '<h4>$1</h4>')
			.replace(/^\* (.*$)/gim, '<li>$1</li>')
			.replace(/\n/g, '<br>');

		preview.html(html);
	},

	generate_table_of_contents: function() {
		var content = this.container.find('#notes-textarea').val();
		var toc = this.container.find('#toc-content');
		var headings = [];

		// Extract headings
		var lines = content.split('\n');
		lines.forEach(function(line, index) {
			var match = line.match(/^(#{1,4})\s+(.+)$/);
			if (match) {
				headings.push({
					level: match[1].length,
					text: match[2],
					line: index
				});
			}
		});

		// Generate TOC HTML
		var tocHtml = '';
		headings.forEach(function(heading) {
			tocHtml += `<div class="toc-item level-${heading.level}" data-line="${heading.line}">
				${heading.text}
			</div>`;
		});

		toc.html(tocHtml);

		// Setup click handlers for TOC items
		var me = this;
		toc.find('.toc-item').on('click', function() {
			var line = $(this).data('line');
			me.scroll_to_line(line);
			toc.find('.toc-item').removeClass('active');
			$(this).addClass('active');
		});
	},

	scroll_to_line: function(lineNumber) {
		var textarea = this.container.find('#notes-textarea')[0];
		var lines = textarea.value.split('\n');
		var position = 0;

		for (var i = 0; i < lineNumber && i < lines.length; i++) {
			position += lines[i].length + 1; // +1 for newline
		}

		textarea.setSelectionRange(position, position);
		textarea.focus();
	},

	render_description_content: function(doctype) {
		var personal_description = this.get_personal_description(doctype.name);
		var has_official = doctype.description && doctype.description.trim();
		var has_personal = personal_description && personal_description.trim();

		var content = '';

		if (has_official) {
			content += '<p class="text-muted official-description">' + doctype.description + '</p>';
			if (has_personal) {
				content += '<div class="personal-description-section">' +
					'<small class="text-info"><i class="fa fa-user"></i> Personal Note:</small>' +
					'<p class="personal-description">' + personal_description + '</p>' +
					'<button class="btn btn-sm btn-outline-secondary" id="edit-personal-description">' +
					'<i class="fa fa-edit"></i> Edit Personal Description</button>' +
					'</div>';
			} else {
				content += '<button class="btn btn-sm btn-outline-primary" id="add-personal-description">' +
					'<i class="fa fa-plus"></i> Add Personal Description</button>';
			}
		} else {
			content += '<p class="text-muted">No description available</p>';
			if (has_personal) {
				content += '<div class="personal-description-section">' +
					'<small class="text-info"><i class="fa fa-user"></i> Personal Description:</small>' +
					'<p class="personal-description">' + personal_description + '</p>' +
					'<button class="btn btn-sm btn-outline-secondary" id="edit-personal-description">' +
					'<i class="fa fa-edit"></i> Edit Personal Description</button>' +
					'</div>';
			} else {
				content += '<button class="btn btn-sm btn-primary" id="add-personal-description">' +
					'<i class="fa fa-plus"></i> Add Personal Description</button>';
			}
		}

		return content;
	},

	get_personal_description: function(doctype_name) {
		return localStorage.getItem('knowlan_personal_desc_' + doctype_name) || '';
	},

	save_personal_description: function(doctype_name, description) {
		localStorage.setItem('knowlan_personal_desc_' + doctype_name, description);
	},

	show_personal_description_modal: function(doctype_name, existing_description) {
		var me = this;
		var is_edit = existing_description && existing_description.trim();

		var modal = new frappe.ui.Dialog({
			title: (is_edit ? 'Edit' : 'Add') + ' Personal Description',
			fields: [{
				fieldtype: 'Small Text',
				fieldname: 'personal_description',
				label: 'Personal Description',
				default: existing_description || '',
				reqd: 1,
				description: 'Add your personal notes or description for this DocType'
			}],
			primary_action_label: is_edit ? 'Update' : 'Add',
			primary_action: function(values) {
				me.save_personal_description(doctype_name, values.personal_description);
				me.refresh_description_section();
				modal.hide();
				frappe.show_alert({
					message: 'Personal description ' + (is_edit ? 'updated' : 'added'),
					indicator: 'green'
				});
			}
		});

		modal.show();
	},

	refresh_description_section: function() {
		if (this.current_doctype && this.current_doctype.doctype) {
			var new_content = this.render_description_content(this.current_doctype.doctype);
			this.container.find('#description-section').html(new_content);
			this.setup_description_handlers();
		}
	},

	setup_description_handlers: function() {
		var me = this;

		this.container.find('#add-personal-description').on('click', function() {
			if (me.current_doctype) {
				me.show_personal_description_modal(me.current_doctype.doctype.name, '');
			}
		});

		this.container.find('#edit-personal-description').on('click', function() {
			if (me.current_doctype) {
				var existing = me.get_personal_description(me.current_doctype.doctype.name);
				me.show_personal_description_modal(me.current_doctype.doctype.name, existing);
			}
		});
	},

	show_doctype_references: function(doctype_name, doctype_files) {
		var me = this;

		// Show loading state
		var code_panel = this.container.find('#code-panel');
		code_panel.html('<div class="text-center p-4"><i class="fa fa-spinner fa-spin"></i> Searching for references...</div>');

		// Search for file and field references
		this.search_doctype_references(doctype_name, function(references) {
			me.display_references(doctype_name, references, doctype_files);
		});
	},

	search_doctype_references: function(doctype_name, callback) {
		var me = this;
		var references = {
			file_references: [],
			field_references: []
		};

		// Search for file references (imports, hooks, etc.)
		frappe.call({
			method: 'knowlan.api.v1.utils.search_codebase',
			args: {
				search_term: doctype_name,
				file_types: ['py', 'js', 'json'],
				search_type: 'file_references'
			},
			callback: function(r) {
				if (r.message && r.message.results) {
					references.file_references = r.message.results;
				}

				// Search for field references
				me.search_field_references(doctype_name, function(field_refs) {
					references.field_references = field_refs;
					callback(references);
				});
			}
		});
	},

	search_field_references: function(doctype_name, callback) {
		var me = this;

		// Get doctype fields first
		if (this.current_doctype && this.current_doctype.fields) {
			var field_names = this.current_doctype.fields.map(f => f.fieldname);
			var field_references = [];
			var completed_searches = 0;

			field_names.forEach(function(fieldname) {
				frappe.call({
					method: 'knowlan.api.v1.utils.search_codebase',
					args: {
						search_term: fieldname,
						file_types: ['py', 'js'],
						search_type: 'field_references',
						doctype_context: doctype_name
					},
					callback: function(r) {
						completed_searches++;
						if (r.message && r.message.results && r.message.results.length > 0) {
							field_references.push({
								field_name: fieldname,
								references: r.message.results
							});
						}

						if (completed_searches === field_names.length) {
							callback(field_references);
						}
					}
				});
			});

			// Handle case where no fields
			if (field_names.length === 0) {
				callback([]);
			}
		} else {
			callback([]);
		}
	},

	display_references: function(doctype_name, references, doctype_files) {
		var me = this;

		var references_html = '<div class="references-container">' +
			'<div class="references-header">' +
			'<h5><i class="fa fa-search"></i> References for ' + doctype_name + '</h5>' +
			'<button class="btn btn-sm btn-default" id="back-to-files">' +
			'<i class="fa fa-arrow-left"></i> Back to Files</button>' +
			'</div>';

		// File References Section
		references_html += '<div class="references-section">' +
			'<h6><i class="fa fa-file-code-o"></i> File References (' + references.file_references.length + ')</h6>';

		if (references.file_references.length > 0) {
			references_html += '<div class="reference-items">';
			references.file_references.forEach(function(ref, index) {
				references_html += '<div class="reference-item">' +
					'<div class="reference-header">' +
					'<strong>' + ref.file_path + '</strong>' +
					'<span class="badge badge-info">' + ref.app + '</span>' +
					'</div>' +
					'<div class="reference-snippet">' +
					'<pre><code>' + me.escape_html(ref.snippet) + '</code></pre>' +
					'</div>' +
					'</div>';
			});
			references_html += '</div>';
		} else {
			references_html += '<p class="text-muted">No file references found.</p>';
		}

		references_html += '</div>';

		// Field References Section
		references_html += '<div class="references-section">' +
			'<h6><i class="fa fa-list"></i> Field References (' + references.field_references.length + ')</h6>';

		if (references.field_references.length > 0) {
			references_html += '<div class="reference-items">';
			references.field_references.forEach(function(field_ref) {
				references_html += '<div class="field-reference-group">' +
					'<h7><strong>Field: ' + field_ref.field_name + '</strong></h7>';

				field_ref.references.forEach(function(ref) {
					references_html += '<div class="reference-item">' +
						'<div class="reference-header">' +
						'<strong>' + ref.file_path + '</strong>' +
						'<span class="badge badge-success">' + ref.app + '</span>' +
						'</div>' +
						'<div class="reference-snippet">' +
						'<pre><code>' + me.escape_html(ref.snippet) + '</code></pre>' +
						'</div>' +
						'</div>';
				});

				references_html += '</div>';
			});
			references_html += '</div>';
		} else {
			references_html += '<p class="text-muted">No field references found.</p>';
		}

		references_html += '</div></div>';

		// Display the references
		var code_panel = this.container.find('#code-panel');
		code_panel.html(references_html);

		// Setup back button
		code_panel.find('#back-to-files').on('click', function() {
			me.show_file_selection(doctype_files, doctype_name);
		});
	},

	escape_html: function(text) {
		var div = document.createElement('div');
		div.textContent = text;
		return div.innerHTML;
	},

	show_field_selection_modal: function(fieldname, row) {
		var me = this;
		var doctype = this.current_doctype.doctype;
		var field_data = this.current_doctype.fields.find(f => f.fieldname === fieldname);

		if (!field_data) return;

		// Create simplified modal content
		var modal_content = '<div class="field-selection-modal-simple">' +
			'<div class="field-header">' +
			'<h4><i class="fa fa-cube"></i> ' + fieldname + '</h4>' +
			'<span class="fieldtype-badge">' + field_data.fieldtype + '</span>' +
			'</div>' +
			'<div class="field-actions-simple">';

		// Add Create Notes File button
		modal_content += '<button class="btn btn-success btn-block mb-2" id="create-field-notes">' +
			'<i class="fa fa-file-text-o"></i> Create Notes File</button>';

		// Add Show References button
		modal_content += '<button class="btn btn-info btn-block mb-2" id="show-field-references">' +
			'<i class="fa fa-search"></i> Show References</button>';

		modal_content += '</div></div>';

		// Create and show modal
		var modal = new frappe.ui.Dialog({
			title: 'Field Options: ' + fieldname,
			fields: [{
				fieldtype: 'HTML',
				fieldname: 'field_content',
				options: modal_content
			}],
			primary_action_label: 'Close'
		});

		modal.show();

		// Setup button handlers
		modal.$wrapper.find('#create-field-notes').on('click', function() {
			me.create_field_notes_file(fieldname);
			modal.hide();
		});

		modal.$wrapper.find('#show-field-references').on('click', function() {
			me.show_field_references_modal(fieldname, doctype.name);
		});
	},

	open_customize_form: function(doctype_name, fieldname) {
		// Open customize form and highlight the field
		frappe.set_route('Form', 'Customize Form', doctype_name);

		// Add a slight delay to allow the form to load, then highlight the field
		setTimeout(function() {
			// This would need to be implemented in the customize form
			frappe.show_alert({
				message: 'Opening customize form for field: ' + fieldname,
				indicator: 'blue'
			});
		}, 1000);
	},

	show_link_details_modal: function(doctype_name, fieldname, fieldtype, link_type) {
			var me = this;

			var modal_title = (link_type === 'outgoing' ? 'Outgoing' : 'Incoming') + ' Link Details';

			var details_modal = new frappe.ui.Dialog({
				title: modal_title,
				size: 'large',
				fields: [{
					fieldtype: 'HTML',
					fieldname: 'link_details',
					options: `
						<div class="link-details-container">
							<div class="link-detail-header">
								<h5><i class="fa fa-link"></i> ${doctype_name}</h5>
								<span class="badge badge-${link_type === 'outgoing' ? 'success' : 'primary'}">${link_type.toUpperCase()}</span>
							</div>
							<div class="link-detail-info">
								<div class="detail-row">
									<span class="detail-label">DocType:</span>
									<span class="detail-value">${doctype_name}</span>
								</div>
								<div class="detail-row">
									<span class="detail-label">Field Name:</span>
									<span class="detail-value">${fieldname}</span>
								</div>
								<div class="detail-row">
									<span class="detail-label">Field Type:</span>
									<span class="detail-value">${fieldtype}</span>
								</div>
								<div class="detail-row">
									<span class="detail-label">Link Direction:</span>
									<span class="detail-value">${link_type === 'outgoing' ? 'This DocType → ' + doctype_name : doctype_name + ' → This DocType'}</span>
								</div>
							</div>
							<div class="link-detail-actions">
								<button class="btn btn-primary" id="view-linked-doctype">
									<i class="fa fa-eye"></i> View ${doctype_name}
								</button>
								<button class="btn btn-secondary" id="customize-linked-doctype">
									<i class="fa fa-cog"></i> Customize ${doctype_name}
								</button>
							</div>
						</div>
					`
				}],
				primary_action_label: 'Close'
			});

			details_modal.show();

			// Setup action handlers
			details_modal.$wrapper.find('#view-linked-doctype').on('click', function() {
				me.select_doctype(doctype_name);
				details_modal.hide();
			});

			details_modal.$wrapper.find('#customize-linked-doctype').on('click', function() {
				me.open_doctype_customize(doctype_name);
			});
		},

	show_field_references_modal: function(fieldname, doctype_name) {
		var me = this;

		// Create enhanced references modal with larger size
		var references_modal = new frappe.ui.Dialog({
			title: 'Field References: ' + fieldname,
			size: 'extra-large',
			fields: [{
				fieldtype: 'HTML',
				fieldname: 'references_content',
				options: '<div class="text-center p-4"><i class="fa fa-spinner fa-spin"></i> Searching for references...</div>'
			}],
			primary_action_label: 'Close'
		});

		references_modal.show();

		// Search for field references
		frappe.call({
			method: 'knowlan.api.v1.utils.search_codebase',
			args: {
				search_term: fieldname,
				file_types: ['py', 'js'],
				search_type: 'field_references',
				doctype_context: doctype_name
			},
			callback: function(r) {
				var references_html = '<div class="field-references-container">';

				if (r.message && r.message.results && r.message.results.length > 0) {
					references_html += '<div class="references-header">' +
						'<h6>Found ' + r.message.results.length + ' references:</h6>' +
						'<div class="references-controls">' +
						'<button class="btn btn-sm btn-primary" id="expand-all-refs">Expand All</button>' +
						'<button class="btn btn-sm btn-secondary" id="collapse-all-refs">Collapse All</button>' +
						'</div>' +
						'</div>';
					references_html += '<div class="reference-items">';

					r.message.results.forEach(function(ref, index) {
						var highlighted_snippet = me.highlight_field_in_code(ref.snippet, fieldname);
						references_html += '<div class="reference-item enhanced-ref-item" data-ref-index="' + index + '">' +
							'<div class="reference-header">' +
							'<div class="ref-file-info">' +
							'<i class="fa fa-file-code-o"></i>' +
							'<strong>' + ref.file_path + '</strong>' +
							'<span class="badge badge-info">' + ref.app + '</span>' +
							'</div>' +
							'<div class="ref-actions">' +
							'<button class="btn btn-xs btn-primary view-full-file" data-file="' + ref.file_path + '" data-app="' + ref.app + '">' +
							'<i class="fa fa-eye"></i> View File' +
							'</button>' +
							'<button class="btn btn-xs btn-secondary toggle-snippet" data-target="snippet-' + index + '">' +
							'<i class="fa fa-expand"></i> Expand' +
							'</button>' +
							'</div>' +
							'</div>' +
							'<div class="reference-snippet collapsed" id="snippet-' + index + '">' +
							'<pre><code class="highlighted-code">' + highlighted_snippet + '</code></pre>' +
							'</div>' +
							'</div>';
					});

					references_html += '</div>';
				} else {
					references_html += '<p class="text-muted">No references found for this field.</p>';
				}

				references_html += '</div>';

				// Update modal content
				references_modal.fields_dict.references_content.$wrapper.html(references_html);

				// Setup enhanced references handlers
				me.setup_enhanced_references_handlers(references_modal);
			}
		});
	},

	highlight_field_in_code: function(code, fieldname) {
		// Highlight the field name in the code snippet
		var escaped_code = this.escape_html(code);
		var field_regex = new RegExp('\\b' + fieldname + '\\b', 'gi');
		return escaped_code.replace(field_regex, '<mark class="field-highlight">$&</mark>');
	},

	setup_enhanced_references_handlers: function(modal) {
		var me = this;
		var $wrapper = modal.fields_dict.references_content.$wrapper;

		// Use event delegation for dynamically added elements
		$wrapper.off('click.references').on('click.references', '#expand-all-refs', function() {
			$wrapper.find('.reference-snippet').removeClass('collapsed');
			$wrapper.find('.toggle-snippet i').removeClass('fa-expand').addClass('fa-compress');
			$wrapper.find('.toggle-snippet').each(function() {
				$(this).html('<i class="fa fa-compress"></i> Collapse');
			});
		});

		$wrapper.off('click.collapse').on('click.collapse', '#collapse-all-refs', function() {
			$wrapper.find('.reference-snippet').addClass('collapsed');
			$wrapper.find('.toggle-snippet i').removeClass('fa-compress').addClass('fa-expand');
			$wrapper.find('.toggle-snippet').each(function() {
				$(this).html('<i class="fa fa-expand"></i> Expand');
			});
		});

		// Individual toggle with event delegation
		$wrapper.off('click.toggle').on('click.toggle', '.toggle-snippet', function() {
			var target = $(this).data('target');
			var snippet = $wrapper.find('#' + target);
			var icon = $(this).find('i');

			if (snippet.hasClass('collapsed')) {
				snippet.removeClass('collapsed');
				icon.removeClass('fa-expand').addClass('fa-compress');
				$(this).html('<i class="fa fa-compress"></i> Collapse');
			} else {
				snippet.addClass('collapsed');
				icon.removeClass('fa-compress').addClass('fa-expand');
				$(this).html('<i class="fa fa-expand"></i> Expand');
			}
		});

		// View full file with event delegation
		$wrapper.off('click.viewfile').on('click.viewfile', '.view-full-file', function() {
			var file_path = $(this).data('file');
			var app_name = $(this).data('app');
			me.show_full_file_viewer_fallback(file_path, app_name, modal);
		});
	},

	show_full_file_viewer: function(file_path, app_name, parent_modal) {
		var me = this;

		// Create file viewer modal
		var file_modal = new frappe.ui.Dialog({
			title: 'File Viewer: ' + file_path,
			size: 'extra-large',
			fields: [{
				fieldtype: 'HTML',
				fieldname: 'file_content',
				options: '<div class="text-center p-4"><i class="fa fa-spinner fa-spin"></i> Loading file...</div>'
			}],
			primary_action_label: 'Close'
		});

		file_modal.show();

		// Load file content
		frappe.call({
			method: 'knowlan.api.v1.utils.get_file_content',
			args: {
				file_path: file_path,
				app_name: app_name
			},
			callback: function(r) {
				if (r.message) {
					var file_html = '<div class="file-viewer-container">' +
						'<div class="file-header">' +
						'<h6><i class="fa fa-file-code-o"></i> ' + file_path + '</h6>' +
						'<span class="badge badge-info">' + app_name + '</span>' +
						'</div>' +
						'<div class="file-content">' +
						'<pre><code class="language-' + me.get_file_language(file_path) + '">' +
						me.escape_html(r.message) + '</code></pre>' +
						'</div>' +
						'</div>';

					file_modal.fields_dict.file_content.$wrapper.html(file_html);
				} else {
					file_modal.fields_dict.file_content.$wrapper.html(
						'<div class="text-center p-4 text-muted">Could not load file content</div>'
					);
				}
			},
			error: function() {
				me.show_full_file_viewer_fallback(file_path, app_name, parent_modal);
			}
		});
	},

	show_full_file_viewer_fallback: function(file_path, app_name, parent_modal) {
		// Create file viewer modal with fallback content
		var file_modal = new frappe.ui.Dialog({
			title: 'File Reference: ' + file_path,
			size: 'large',
			fields: [{
				fieldtype: 'HTML',
				fieldname: 'file_content',
				options: '<div class="file-viewer-container">' +
					'<div class="file-header-enhanced">' +
					'<div class="file-info-container">' +
					'<div class="file-property">' +
					'<span class="property-label">File:</span>' +
					'<span class="property-value">' + file_path + '</span>' +
					'</div>' +
					'<div class="file-property">' +
					'<span class="property-label">App:</span>' +
					'<span class="property-value">' + app_name + '</span>' +
					'</div>' +
					'</div>' +
					'</div>' +
					'<div class="file-content-fallback">' +
					'<div class="alert alert-info">' +
					'<i class="fa fa-info-circle"></i> File content viewing is not available through API. ' +
					'<br>Use Code Search to explore this file.' +
					'</div>' +
					'<div class="file-actions">' +
					'<button class="btn btn-primary" onclick="frappe.set_route(\'code-search\');">' +
					'<i class="fa fa-search"></i> Open Code Search' +
					'</button>' +
					'<button class="btn btn-secondary" onclick="window.open(\'http://knowlan-site:8010/app/code-search\', \'_blank\');">' +
					'<i class="fa fa-external-link"></i> Open in New Tab' +
					'</button>' +
					'</div>' +
					'</div>' +
					'</div>'
			}],
			primary_action_label: 'Close'
		});

		file_modal.show();
	},

	get_file_language: function(file_path) {
		var extension = file_path.split('.').pop().toLowerCase();
		var language_map = {
			'py': 'python',
			'js': 'javascript',
			'json': 'json',
			'html': 'html',
			'css': 'css',
			'scss': 'scss',
			'md': 'markdown'
		};
		return language_map[extension] || 'text';
	},

	setup_notes_handlers: function() {
		var me = this;

		// Setup markdown editor with live rendering
		this.container.find('#markdown-editor').on('input', function() {
			var raw_content = me.get_editor_raw_content();
			var rendered_content = me.render_markdown_content(raw_content);
			$(this).html(rendered_content);

			// Save content
			if (me.current_doctype) {
				localStorage.setItem(`knowlan_notes_${me.current_doctype.doctype.name}`, raw_content);
			}

			// Update TOC
			me.generate_table_of_contents();
		});

		// Handle formatting buttons
		this.container.find('.formatting-buttons .btn').on('click', function() {
			var format = $(this).data('format');
			me.apply_markdown_formatting(format);
		});

		// Handle add page
		this.container.find('#add-page').on('click', function() {
			me.show_add_page_modal();
		});

		// Handle TOC reload
		this.container.find('#reload-toc').on('click', function() {
			me.generate_table_of_contents();
		});

		// Handle keyboard shortcuts
		this.container.find('#markdown-editor').on('keydown', function(e) {
			if (e.ctrlKey || e.metaKey) {
				switch(e.key) {
					case 'b':
						e.preventDefault();
						me.apply_markdown_formatting('bold');
						break;
					case 'i':
						e.preventDefault();
						me.apply_markdown_formatting('italic');
						break;
				}
			}
		});
	},

	get_editor_raw_content: function() {
		var editor = this.container.find('#markdown-editor');
		return editor.data('raw-content') || editor.text();
	},

	apply_markdown_formatting: function(format) {
		var editor = this.container.find('#markdown-editor')[0];
		var selection = window.getSelection();
		var selectedText = selection.toString();

		var formatted_text = '';

		switch(format) {
			case 'bold':
				formatted_text = selectedText ? `**${selectedText}**` : '**bold text**';
				break;
			case 'italic':
				formatted_text = selectedText ? `*${selectedText}*` : '*italic text*';
				break;
			case 'heading':
				formatted_text = selectedText ? `## ${selectedText}` : '## Heading';
				break;
			case 'list':
				formatted_text = selectedText ? `- ${selectedText}` : '- List item';
				break;
			case 'code':
				formatted_text = selectedText ? `\`${selectedText}\`` : '`code`';
				break;
			case 'codeblock':
				formatted_text = selectedText ? `\`\`\`\n${selectedText}\n\`\`\`` : '```\ncode block\n```';
				break;
			case 'link':
				formatted_text = selectedText ? `[${selectedText}](url)` : '[link text](url)';
				break;
			case 'table':
				formatted_text = '| Header 1 | Header 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |';
				break;
		}

		if (selectedText) {
			// Replace selected text
			var range = selection.getRangeAt(0);
			range.deleteContents();
			range.insertNode(document.createTextNode(formatted_text));
		} else {
			// Insert at cursor position
			document.execCommand('insertText', false, formatted_text);
		}

		// Update raw content and re-render
		var raw_content = editor.textContent;
		$(editor).data('raw-content', raw_content);
		$(editor).html(this.render_markdown_content(raw_content));
	},

	render_documentation_content: function(doctype) {
		var has_online_docs = this.has_online_documentation(doctype.name);
		var user_docs = this.get_user_documentation(doctype.name);

		var content = '<div class="documentation-actions">';

		// Always show both buttons, but style them differently based on availability
		if (has_online_docs) {
			content += '<button class="btn btn-sm btn-success" id="open-documentation">' +
				'<i class="fa fa-book"></i> Open Documentation</button>';
			content += '<button class="btn btn-sm btn-outline-primary ml-2" id="add-documentation-link">' +
				'<i class="fa fa-plus"></i> Add Online Link</button>';
		} else {
			content += '<button class="btn btn-sm btn-outline-secondary" id="open-documentation" disabled>' +
				'<i class="fa fa-book"></i> No Official Docs</button>';
			content += '<button class="btn btn-sm btn-primary ml-2" id="add-documentation-link">' +
				'<i class="fa fa-plus"></i> Add Online Link</button>';
		}

		if (user_docs.length > 0) {
			content += '<button class="btn btn-sm btn-info ml-2" id="view-user-docs">' +
				'<i class="fa fa-users"></i> View User Links (' + user_docs.length + ')</button>';
		}

		content += '</div>';

		return content;
	},

	has_online_documentation: function(doctype_name) {
		// Check if doctype has known online documentation
		var documented_doctypes = [
			'User', 'Customer', 'Supplier', 'Item', 'Sales Order', 'Purchase Order',
			'Sales Invoice', 'Purchase Invoice', 'Payment Entry', 'Journal Entry',
			'Stock Entry', 'Delivery Note', 'Purchase Receipt', 'Quotation',
			'Lead', 'Opportunity', 'Project', 'Task', 'Issue', 'Employee',
			'Salary Structure', 'Salary Slip', 'Leave Application', 'Expense Claim'
		];

		return documented_doctypes.includes(doctype_name);
	},

	get_documentation_url: function(doctype_name) {
		// Return the official documentation URL for the doctype
		var base_url = 'https://docs.frappe.io/erpnext/user/manual/en/';
		var doctype_mappings = {
			// Users and Permissions
			'User': 'setting-up/users-and-permissions/adding-users',

			// CRM
			'Customer': 'CRM/customer',
			'Lead': 'CRM/lead',
			'Opportunity': 'CRM/opportunity',
			'Contact': 'CRM/contact',
			'Address': 'CRM/address',

			// Selling
			'Sales Order': 'selling/sales-order',
			'Quotation': 'selling/quotation',
			'Delivery Note': 'selling/delivery-note',
			'Sales Invoice': 'accounts/sales-invoice',
			'Sales Person': 'selling/sales-person',
			'Customer Group': 'selling/customer-group',
			'Territory': 'selling/territory',

			// Buying
			'Supplier': 'buying/supplier',
			'Purchase Order': 'buying/purchase-order',
			'Purchase Receipt': 'buying/purchase-receipt',
			'Purchase Invoice': 'accounts/purchase-invoice',
			'Request for Quotation': 'buying/request-for-quotation',
			'Supplier Quotation': 'buying/supplier-quotation',

			// Stock
			'Item': 'stock/item',
			'Stock Entry': 'stock/stock-entry',
			'Item Group': 'stock/item-group',
			'Warehouse': 'stock/warehouse',
			'Stock Reconciliation': 'stock/stock-reconciliation',
			'Material Request': 'stock/material-request',

			// Accounts
			'Payment Entry': 'accounts/payment-entry',
			'Journal Entry': 'accounts/journal-entry',
			'Chart of Accounts': 'accounts/chart-of-accounts',
			'Cost Center': 'accounts/cost-center',
			'Fiscal Year': 'accounts/fiscal-year',
			'Payment Terms': 'accounts/payment-terms',
			'Payment Request': 'accounts/payment-request',

			// Projects
			'Project': 'projects/project',
			'Task': 'projects/task',
			'Timesheet': 'projects/timesheet',
			'Project Type': 'projects/project-type',

			// Human Resources
			'Employee': 'human-resources/employee',
			'Salary Structure': 'human-resources/salary-structure',
			'Salary Slip': 'human-resources/salary-slip',
			'Leave Application': 'human-resources/leave-application',
			'Expense Claim': 'human-resources/expense-claim',
			'Attendance': 'human-resources/attendance',
			'Employee Checkin': 'human-resources/employee-checkin',
			'Leave Type': 'human-resources/leave-type',
			'Holiday List': 'human-resources/holiday-list',

			// Support
			'Issue': 'support/issue',
			'Warranty Claim': 'support/warranty-claim',
			'Maintenance Schedule': 'support/maintenance-schedule',
			'Maintenance Visit': 'support/maintenance-visit',

			// Manufacturing
			'BOM': 'manufacturing/bill-of-materials',
			'Work Order': 'manufacturing/work-order',
			'Production Plan': 'manufacturing/production-plan',
			'Item Alternative': 'manufacturing/item-alternative',

			// Website
			'Website Settings': 'website/website-settings',
			'Blog Post': 'website/blog-post',
			'Web Page': 'website/web-page'
		};

		return base_url + (doctype_mappings[doctype_name] || '');
	},

	get_user_documentation: function(doctype_name) {
		var stored_docs = localStorage.getItem('knowlan_user_docs_' + doctype_name);
		return stored_docs ? JSON.parse(stored_docs) : [];
	},

	save_user_documentation: function(doctype_name, doc_data) {
		var existing_docs = this.get_user_documentation(doctype_name);
		existing_docs.push(doc_data);
		localStorage.setItem('knowlan_user_docs_' + doctype_name, JSON.stringify(existing_docs));
	},

	setup_documentation_handlers: function() {
		var me = this;

		this.container.find('#open-documentation').on('click', function() {
			if (me.current_doctype) {
				var url = me.get_documentation_url(me.current_doctype.doctype.name);
				if (url) {
					window.open(url, '_blank');
				}
			}
		});

		this.container.find('#add-documentation-link').on('click', function() {
			if (me.current_doctype) {
				me.show_add_documentation_modal(me.current_doctype.doctype.name);
			}
		});

		this.container.find('#view-user-docs').on('click', function() {
			if (me.current_doctype) {
				me.show_user_documentation_modal(me.current_doctype.doctype.name);
			}
		});
	},

	show_add_documentation_modal: function(doctype_name) {
		var me = this;

		var modal = new frappe.ui.Dialog({
			title: 'Add Documentation Link',
			fields: [{
				fieldtype: 'Data',
				fieldname: 'link',
				label: 'Link',
				reqd: 1,
				description: 'URL to the documentation'
			}, {
				fieldtype: 'Data',
				fieldname: 'caption',
				label: 'Caption',
				reqd: 1,
				description: 'Short title for the link'
			}, {
				fieldtype: 'Small Text',
				fieldname: 'summary',
				label: 'Summary Description',
				description: 'Brief description of what this documentation covers'
			}],
			primary_action_label: 'Add Link',
			primary_action: function(values) {
				var doc_data = {
					link: values.link,
					caption: values.caption,
					summary: values.summary || '',
					added_by: frappe.session.user,
					added_on: new Date().toISOString()
				};

				me.save_user_documentation(doctype_name, doc_data);
				me.refresh_documentation_section();
				modal.hide();

				frappe.show_alert({
					message: 'Documentation link added successfully',
					indicator: 'green'
				});
			}
		});

		modal.show();
	},

	show_user_documentation_modal: function(doctype_name) {
		var user_docs = this.get_user_documentation(doctype_name);

		var docs_html = '<div class="user-docs-container">';

		if (user_docs.length > 0) {
			user_docs.forEach(function(doc, index) {
				docs_html += '<div class="user-doc-item">' +
					'<h6><a href="' + doc.link + '" target="_blank">' + doc.caption + '</a></h6>' +
					'<p class="text-muted">' + (doc.summary || 'No summary provided') + '</p>' +
					'<small class="text-info">Added by ' + doc.added_by + ' on ' +
					new Date(doc.added_on).toLocaleDateString() + '</small>' +
					'</div>';
			});
		} else {
			docs_html += '<p class="text-muted">No user documentation links available.</p>';
		}

		docs_html += '</div>';

		var modal = new frappe.ui.Dialog({
			title: 'User Documentation Links',
			fields: [{
				fieldtype: 'HTML',
				fieldname: 'docs_content',
				options: docs_html
			}],
			primary_action_label: 'Close'
		});

		modal.show();
	},

	refresh_documentation_section: function() {
		if (this.current_doctype && this.current_doctype.doctype) {
			var new_content = this.render_documentation_content(this.current_doctype.doctype);
			this.container.find('#documentation-section').html(new_content);
			this.setup_documentation_handlers();
		}
	},

	setup_stat_card_handlers: function() {
		var me = this;

		this.container.find('.clickable-stat').on('click', function() {
			var action = $(this).data('action');
			if (action === 'open-list' && me.current_doctype) {
				var doctype_name = me.current_doctype.doctype.name;
				frappe.set_route('List', doctype_name);
				frappe.show_alert({
					message: 'Opening ' + doctype_name + ' List',
					indicator: 'blue'
				}, 2);
			}
		});
	},

	setup_breadcrumb_handlers: function() {
		var me = this;

		this.container.find('.breadcrumb-link').on('click', function(e) {
			e.preventDefault();

			var type = $(this).data('type');
			var name = $(this).data('name');
			var app = $(this).data('app');

			if (type === 'app' && name) {
				me.show_app_overview(name);
			} else if (type === 'module' && name && app) {
				me.show_module_overview(app, name);
			}
		});
	},

	show_app_overview: function(app_name) {
		// Update selection state
		this.update_selection_state('app', {
			app_name: app_name
		});

		// Switch to overview tab
		this.container.find('#view-overview').click();

		this.render_app_overview(app_name);

		// Update breadcrumb
		this.update_breadcrumb([app_name], {
			app_name: app_name
		});
	},

	show_module_overview: function(app_name, module_name) {
		// Update selection state
		this.update_selection_state('module', {
			app_name: app_name,
			module_name: module_name
		});

		// Switch to overview tab
		this.container.find('#view-overview').click();

		this.render_module_overview(app_name, module_name);

		// Update breadcrumb
		this.update_breadcrumb([app_name, module_name], {
			app_name: app_name,
			module_name: module_name
		});
	},

	// Page Management System
	current_page: 'main',
	pages: {},

	show_add_page_modal: function() {
		var me = this;

		var modal = new frappe.ui.Dialog({
			title: 'Add New Page',
			fields: [{
				fieldtype: 'Data',
				fieldname: 'page_name',
				label: 'Page Name',
				reqd: 1,
				description: 'Enter a name for the new page'
			}],
			primary_action_label: 'Create Page',
			primary_action: function(values) {
				me.create_new_page(values.page_name);
				modal.hide();
			}
		});

		modal.show();
	},

	create_new_page: function(page_name) {
		if (!this.current_doctype) return;

		var doctype_name = this.current_doctype.doctype.name;
		var page_key = this.get_page_key(doctype_name, page_name);

		// Save current page content before switching
		this.save_current_page();

		// Create new page with initial content
		var initial_content = '# ' + page_name + '\n\n';
		localStorage.setItem(page_key, initial_content);
		this.pages[page_key] = initial_content;
		this.current_page = page_name;

		// Load new page content
		this.container.find('#notes-textarea').val(initial_content);

		// Update pages list immediately
		this.render_pages_list();

		// Generate TOC for new content
		this.generate_table_of_contents();

		frappe.show_alert({
			message: 'Page "' + page_name + '" created',
			indicator: 'green'
		}, 2);
	},

	get_page_key: function(doctype_name, page_name) {
		return 'knowlan_page_' + doctype_name + '_' + page_name.replace(/[^a-zA-Z0-9]/g, '_');
	},

	save_current_page: function() {
		if (!this.current_doctype) return;

		var doctype_name = this.current_doctype.doctype.name;
		var content = this.container.find('#notes-textarea').val();

		if (this.current_page === 'main') {
			// Save to main notes
			localStorage.setItem('knowlan_notes_' + doctype_name, content);
		} else {
			// Save to page
			var page_key = this.get_page_key(doctype_name, this.current_page);
			localStorage.setItem(page_key, content);
			this.pages[page_key] = content;
		}
	},

	load_page: function(page_name) {
		if (!this.current_doctype) return;

		// Save current page before switching
		this.save_current_page();

		var doctype_name = this.current_doctype.doctype.name;
		var content = '';

		if (page_name === 'main') {
			content = localStorage.getItem('knowlan_notes_' + doctype_name) || '';
		} else {
			var page_key = this.get_page_key(doctype_name, page_name);
			content = localStorage.getItem(page_key) || '';
			this.pages[page_key] = content;
		}

		this.current_page = page_name;
		this.container.find('#notes-textarea').val(content);
		this.generate_table_of_contents();
		this.render_pages_list();
	},

	render_pages_list: function() {
		if (!this.current_doctype) return;

		var doctype_name = this.current_doctype.doctype.name;
		var pages_html = '';

		// Add main page
		var main_active = this.current_page === 'main' ? 'active' : '';
		pages_html += '<div class="page-item ' + main_active + '" data-page="main">' +
			'<i class="fa fa-file-text-o"></i> Main Page</div>';

		// Add other pages
		for (var key in localStorage) {
			if (key.startsWith('knowlan_page_' + doctype_name + '_')) {
				var page_name = key.replace('knowlan_page_' + doctype_name + '_', '').replace(/_/g, ' ');
				var active = this.current_page === page_name ? 'active' : '';
				pages_html += '<div class="page-item ' + active + '" data-page="' + page_name + '">' +
					'<i class="fa fa-file-o"></i> ' + page_name +
					'<button class="btn btn-sm btn-outline-danger delete-page" data-page="' + page_name + '" title="Delete Page">' +
					'<i class="fa fa-trash"></i></button></div>';
			}
		}

		this.container.find('#pages-list').html(pages_html);
		this.setup_page_handlers();
	},

	setup_page_handlers: function() {
		var me = this;

		// Page click handlers
		this.container.find('.page-item').on('click', function(e) {
			if ($(e.target).hasClass('delete-page') || $(e.target).parent().hasClass('delete-page')) {
				return; // Don't switch page when deleting
			}

			var page_name = $(this).data('page');
			me.load_page(page_name);
		});

		// Delete page handlers
		this.container.find('.delete-page').on('click', function(e) {
			e.stopPropagation();
			var page_name = $(this).data('page');
			me.delete_page(page_name);
		});
	},

	delete_page: function(page_name) {
		var me = this;

		frappe.confirm(
			'Are you sure you want to delete the page "' + page_name + '"?',
			function() {
				if (!me.current_doctype) return;

				var doctype_name = me.current_doctype.doctype.name;
				var page_key = me.get_page_key(doctype_name, page_name);

				// Remove from localStorage
				localStorage.removeItem(page_key);
				delete me.pages[page_key];

				// Switch to main page if current page is being deleted
				if (me.current_page === page_name) {
					me.load_page('main');
				}

				// Always update pages list after deletion
				me.render_pages_list();

				frappe.show_alert({
					message: 'Page "' + page_name + '" deleted',
					indicator: 'orange'
				}, 2);
			}
		);
	},

	// Obsidian-style Linking System
	handle_obsidian_linking: function() {
		var textarea = this.container.find('#notes-textarea')[0];
		var content = textarea.value;
		var cursorPos = textarea.selectionStart;

		// Check if we just typed ]] to complete a link
		if (content.substring(cursorPos - 2, cursorPos) === ']]') {
			this.process_completed_link(content, cursorPos);
		}

		// Always regenerate TOC on content change
		this.generate_table_of_contents();
	},

	process_completed_link: function(content, cursorPos) {
		// Find the opening [[ before the cursor
		var beforeCursor = content.substring(0, cursorPos - 2);
		var lastOpenBracket = beforeCursor.lastIndexOf('[[');

		if (lastOpenBracket !== -1) {
			var linkText = content.substring(lastOpenBracket + 2, cursorPos - 2);

			if (linkText.trim()) {
				this.show_link_creation_modal(linkText.trim());
			}
		}
	},

	show_link_creation_modal: function(link_text) {
		var me = this;

		var modal = new frappe.ui.Dialog({
			title: 'Create Linked Page',
			fields: [{
				fieldtype: 'Data',
				fieldname: 'page_name',
				label: 'Page Name',
				default: link_text,
				reqd: 1,
				description: 'Name for the new linked page'
			}, {
				fieldtype: 'Check',
				fieldname: 'create_page',
				label: 'Create New Page',
				default: 1,
				description: 'Create a new page with this name'
			}],
			primary_action_label: 'Create Link',
			primary_action: function(values) {
				if (values.create_page) {
					me.create_linked_page(values.page_name);
				}
				modal.hide();
			}
		});

		modal.show();
	},

	create_linked_page: function(page_name) {
		if (!this.current_doctype) return;

		var doctype_name = this.current_doctype.doctype.name;
		var page_key = this.get_page_key(doctype_name, page_name);

		// Check if page already exists
		if (localStorage.getItem(page_key)) {
			frappe.show_alert({
				message: 'Page "' + page_name + '" already exists',
				indicator: 'orange'
			}, 2);
			return;
		}

		// Create new page
		localStorage.setItem(page_key, '# ' + page_name + '\n\n');
		this.pages[page_key] = '# ' + page_name + '\n\n';

		// Update pages list
		this.render_pages_list();

		frappe.show_alert({
			message: 'Linked page "' + page_name + '" created',
			indicator: 'green'
		}, 2);
	},

	// Enhanced link detection for rendering
	render_obsidian_links: function(content) {
		// Replace [[page_name]] with clickable links
		return content.replace(/\[\[([^\]]+)\]\]/g, function(match, page_name) {
			return '<a href="#" class="obsidian-link" data-page="' + page_name + '">' + page_name + '</a>';
		});
	},

	setup_obsidian_link_handlers: function() {
		var me = this;

		this.container.find('.obsidian-link').on('click', function(e) {
			e.preventDefault();
			var page_name = $(this).data('page');
			me.navigate_to_linked_page(page_name);
		});
	},

	navigate_to_linked_page: function(page_name) {
		if (!this.current_doctype) return;

		var doctype_name = this.current_doctype.doctype.name;
		var page_key = this.get_page_key(doctype_name, page_name);

		// Check if page exists
		if (localStorage.getItem(page_key)) {
			this.load_page(page_name);
			frappe.show_alert({
				message: 'Navigated to "' + page_name + '"',
				indicator: 'blue'
			}, 2);
		} else {
			// Offer to create the page
			var me = this;
			frappe.confirm(
				'Page "' + page_name + '" does not exist. Would you like to create it?',
				function() {
					me.create_linked_page(page_name);
					me.load_page(page_name);
				}
			);
		}
	},

	// Notes Mode Management
	notes_mode: 'edit',

	switch_to_edit_mode: function() {
		this.notes_mode = 'edit';

		// Update button states
		this.container.find('#edit-mode').removeClass('btn-outline-primary').addClass('btn-primary');
		this.container.find('#view-mode').removeClass('btn-primary').addClass('btn-outline-primary');
		this.container.find('#split-mode').removeClass('btn-primary').addClass('btn-outline-primary');

		// Show editor, hide viewer
		this.container.find('#notes-textarea').show().removeClass('split-editor');
		this.container.find('.formatting-buttons').show();
		this.container.find('#notes-viewer').hide();
		this.container.find('#live-preview').hide();

		// Show sidebar
		this.container.find('#notes-sidebar').show();
		this.hide_toc_toggle_button();
	},

	switch_to_split_mode: function() {
		this.notes_mode = 'split';

		// Update button states
		this.container.find('#split-mode').removeClass('btn-outline-primary').addClass('btn-primary');
		this.container.find('#edit-mode').removeClass('btn-primary').addClass('btn-outline-primary');
		this.container.find('#view-mode').removeClass('btn-primary').addClass('btn-outline-primary');

		// Setup split view
		this.container.find('#notes-textarea').show().addClass('split-editor');
		this.container.find('.formatting-buttons').show();

		// Create live preview panel
		if (this.container.find('#live-preview').length === 0) {
			this.container.find('#notes-textarea').after('<div id="live-preview" class="live-preview"></div>');
		}
		this.container.find('#live-preview').show();
		this.container.find('#notes-viewer').hide();

		// Show sidebar
		this.container.find('#notes-sidebar').show();
		this.hide_toc_toggle_button();

		// Update live preview
		this.update_live_preview();
	},

	switch_to_view_mode: function() {
		this.notes_mode = 'view';

		// Update button states
		this.container.find('#view-mode').removeClass('btn-outline-primary').addClass('btn-primary');
		this.container.find('#edit-mode').removeClass('btn-primary').addClass('btn-outline-primary');
		this.container.find('#split-mode').removeClass('btn-primary').addClass('btn-outline-primary');

		// Hide editor, show viewer
		this.container.find('#notes-textarea').hide().removeClass('split-editor');
		this.container.find('.formatting-buttons').hide();
		this.container.find('#live-preview').hide();

		// Create or update viewer
		this.render_notes_viewer();

		// Show knowledge vault browser
		this.render_knowledge_vault();
	},

	render_notes_viewer: function() {
		var content = this.container.find('#notes-textarea').val();

		if (this.container.find('#notes-viewer').length === 0) {
			this.container.find('#notes-textarea').after('<div id="notes-viewer" class="notes-viewer"></div>');
		}

		// Render markdown with Obsidian links
		var rendered_content = this.render_markdown_content(content);
		rendered_content = this.render_obsidian_links(rendered_content);

		this.container.find('#notes-viewer').html(rendered_content);
		this.container.find('#notes-viewer').show();

		// Setup link handlers
		this.setup_obsidian_link_handlers();
	},

	render_markdown_content: function(content) {
		// Enhanced markdown rendering with proper code block and table support
		var html = content;

		// Handle code blocks with language syntax highlighting
		html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, function(match, lang, code) {
			var language = lang || 'text';
			return '<pre class="code-block"><code class="language-' + language + '">' +
				code.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</code></pre>';
		});

		// Handle tables
		html = html.replace(/^\|(.+)\|\s*$/gm, function(match, content) {
			var cells = content.split('|').map(cell => cell.trim());
			var cellsHtml = cells.map(cell => {
				if (cell.match(/^-+$/)) {
					return ''; // Skip separator rows
				}
				return '<td>' + cell + '</td>';
			}).filter(cell => cell).join('');
			return cellsHtml ? '<tr>' + cellsHtml + '</tr>' : '';
		});

		// Wrap table rows in table tags
		html = html.replace(/(<tr>.*<\/tr>)/gs, '<table class="table table-bordered">$1</table>');

		// Other markdown elements
		html = html
			.replace(/==(.*?)==/g, '<mark>$1</mark>')           // Highlights
			.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')   // Bold
			.replace(/\*(.*?)\*/g, '<em>$1</em>')               // Italic
			.replace(/<u>(.*?)<\/u>/g, '<u>$1</u>')             // Underline
			.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>') // Inline code
			.replace(/^# (.*$)/gim, '<h1>$1</h1>')              // H1
			.replace(/^## (.*$)/gim, '<h2>$1</h2>')             // H2
			.replace(/^### (.*$)/gim, '<h3>$1</h3>')            // H3
			.replace(/^#### (.*$)/gim, '<h4>$1</h4>')           // H4
			.replace(/^\* (.*$)/gim, '<li>$1</li>')             // Lists
			.replace(/^- (.*$)/gim, '<li>$1</li>')              // Lists
			.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" class="markdown-link">$1</a>') // Links
			.replace(/^---$/gm, '<hr>')                         // Horizontal rules
			.replace(/\n/g, '<br>');                            // Line breaks

		// Wrap consecutive list items in ul tags
		html = html.replace(/(<li>.*?<\/li>(?:<br><li>.*?<\/li>)*)/g, '<ul>$1</ul>');
		html = html.replace(/<br>(?=<li>)/g, ''); // Remove br before li
		html = html.replace(/(?<=<\/li>)<br>/g, ''); // Remove br after li

		return html;
	},

	render_knowledge_vault: function() {
		if (!this.current_doctype) return;

		var doctype_name = this.current_doctype.doctype.name;
		var app_name = this.current_doctype.app_name || 'Unknown App';
		var module_name = this.current_doctype.doctype.module || 'Unknown Module';

		// Ensure folder structure exists
		this.ensure_folder_structure(app_name, module_name, doctype_name);

		var vault_html = '<div class="knowledge-vault">' +
			'<h6><i class="fa fa-folder-open"></i> Knowledge Vault</h6>' +
			'<div class="vault-structure">';

		vault_html += '<div class="vault-folder app-folder clickable-folder" data-type="app" data-name="' + app_name + '">' +
			'<i class="fa fa-folder"></i> ' + app_name;

		// Check if app default file exists
		var app_file_key = 'knowlan_app_' + app_name.replace(/[^a-zA-Z0-9]/g, '_');
		if (localStorage.getItem(app_file_key)) {
			vault_html += '<div class="vault-file default-file" data-page="app-default" data-app="' + app_name + '">' +
				'<i class="fa fa-file-text-o"></i> ' + app_name + '.md</div>';
		}

		vault_html += '<div class="vault-folder module-folder clickable-folder" data-type="module" data-name="' + module_name + '" data-app="' + app_name + '">' +
			'<i class="fa fa-folder"></i> ' + module_name;

		// Check if module default file exists
		var module_file_key = 'knowlan_module_' + app_name.replace(/[^a-zA-Z0-9]/g, '_') + '_' + module_name.replace(/[^a-zA-Z0-9]/g, '_');
		if (localStorage.getItem(module_file_key)) {
			vault_html += '<div class="vault-file default-file" data-page="module-default" data-module="' + module_name + '" data-app="' + app_name + '">' +
				'<i class="fa fa-file-text-o"></i> ' + module_name + '.md</div>';
		}

		vault_html += '<div class="vault-folder doctype-folder clickable-folder" data-type="doctype" data-name="' + doctype_name + '">' +
			'<i class="fa fa-folder"></i> ' + doctype_name;

		// Check if doctype default file exists
		var doctype_file_key = 'knowlan_doctype_' + doctype_name.replace(/[^a-zA-Z0-9]/g, '_');
		if (localStorage.getItem(doctype_file_key)) {
			vault_html += '<div class="vault-file default-file" data-page="doctype-default" data-doctype="' + doctype_name + '">' +
				'<i class="fa fa-file-text-o"></i> ' + doctype_name + '.md</div>';
		}

		vault_html += '<div class="vault-files">';

		// Add main page
		vault_html += '<div class="vault-file" data-page="main">' +
			'<i class="fa fa-file-text-o"></i> Main Page</div>';

		// Add other pages
		for (var key in localStorage) {
			if (key.startsWith('knowlan_page_' + doctype_name + '_')) {
				var page_name = key.replace('knowlan_page_' + doctype_name + '_', '').replace(/_/g, ' ');
				vault_html += '<div class="vault-file" data-page="' + page_name + '">' +
					'<i class="fa fa-file-o"></i> ' + page_name + '</div>';
			}
		}

		vault_html += '</div></div></div></div></div></div>';

		// Replace sidebar content with vault
		this.container.find('#notes-sidebar').html(vault_html);

		// Setup vault handlers
		this.setup_vault_handlers();
	},

	setup_vault_handlers: function() {
		var me = this;

		// Handle regular file clicks
		this.container.find('.vault-file:not(.default-file)').on('click', function() {
			var page_name = $(this).data('page');
			me.switch_to_edit_mode();
			setTimeout(() => {
				me.load_page(page_name);
			}, 100);
		});

		// Handle default file clicks
		this.container.find('.vault-file.default-file').on('click', function() {
			var page_type = $(this).data('page');
			var app_name = $(this).data('app');
			var module_name = $(this).data('module');
			var doctype_name = $(this).data('doctype');

			if (page_type === 'app-default') {
				me.load_default_file('app', app_name, app_name, null);
			} else if (page_type === 'module-default') {
				me.load_default_file('module', module_name, app_name, module_name);
			} else if (page_type === 'doctype-default') {
				me.load_default_file('doctype', doctype_name, null, null);
			}
		});

		// Handle folder clicks
		this.container.find('.clickable-folder').on('click', function() {
			var type = $(this).data('type');
			var name = $(this).data('name');
			var app_name = $(this).data('app');

			// Create default file if it doesn't exist and open it
			if (type === 'app') {
				me.ensure_app_default_file(name);
				me.load_default_file('app', name, name, null);
			} else if (type === 'module') {
				me.ensure_module_default_file(app_name, name);
				me.load_default_file('module', name, app_name, name);
			} else if (type === 'doctype') {
				me.ensure_doctype_default_file(name);
				me.load_default_file('doctype', name, null, null);
			}
		});
	},

	// Field-based File Creation
	create_field_notes_file: function(fieldname) {
		if (!this.current_doctype) return;

		var doctype_name = this.current_doctype.doctype.name;
		var field_data = this.current_doctype.fields.find(f => f.fieldname === fieldname);
		var page_name = 'Field: ' + fieldname;
		var page_key = this.get_page_key(doctype_name, page_name);

		// Check if page already exists
		if (localStorage.getItem(page_key)) {
			frappe.show_alert({
				message: 'Notes file for field "' + fieldname + '" already exists',
				indicator: 'orange'
			}, 3);

			// Switch to the existing page
			this.load_page(page_name);
			// Switch to notes tab
			this.container.find('#view-notes').click();
			return;
		}

		// Create initial content for the field notes
		var initial_content = '# Field: ' + fieldname + '\n\n' +
			'**Label:** ' + (field_data.label || 'None') + '\n' +
			'**Type:** ' + field_data.fieldtype + '\n' +
			'**Options:** ' + (field_data.options || 'None') + '\n\n' +
			'## Notes\n\n' +
			'Add your notes about this field here...\n\n' +
			'## Usage Examples\n\n' +
			'## Related Fields\n\n' +
			'## Important Points\n\n';

		// Save the new page
		localStorage.setItem(page_key, initial_content);
		this.pages[page_key] = initial_content;

		// Switch to notes tab first
		this.container.find('#view-notes').click();

		// Then switch to the new page
		setTimeout(() => {
			this.load_page(page_name);
		}, 100);

		frappe.show_alert({
			message: 'Notes file created for field "' + fieldname + '"',
			indicator: 'green'
		}, 3);
	},

	// Hierarchical Folder Structure System
	ensure_folder_structure: function(app_name, module_name, doctype_name) {
		// Create default files for each level if they don't exist
		this.ensure_app_default_file(app_name);
		this.ensure_module_default_file(app_name, module_name);
		this.ensure_doctype_default_file(doctype_name);
	},

	ensure_app_default_file: function(app_name) {
		var app_file_key = 'knowlan_app_' + app_name.replace(/[^a-zA-Z0-9]/g, '_');

		if (!localStorage.getItem(app_file_key)) {
			var default_content = '# ' + app_name + ' Application\n\n' +
				'## Overview\n\n' +
				'This is the main documentation file for the ' + app_name + ' application.\n\n' +
				'## Modules\n\n' +
				'## Key Features\n\n' +
				'## Architecture Notes\n\n' +
				'## Development Guidelines\n\n';

			localStorage.setItem(app_file_key, default_content);
		}
	},

	ensure_module_default_file: function(app_name, module_name) {
		var module_file_key = 'knowlan_module_' + app_name.replace(/[^a-zA-Z0-9]/g, '_') + '_' + module_name.replace(/[^a-zA-Z0-9]/g, '_');

		if (!localStorage.getItem(module_file_key)) {
			var default_content = '# ' + module_name + ' Module\n\n' +
				'**Application:** ' + app_name + '\n\n' +
				'## Overview\n\n' +
				'This is the main documentation file for the ' + module_name + ' module.\n\n' +
				'## DocTypes\n\n' +
				'## Business Logic\n\n' +
				'## Workflows\n\n' +
				'## Integration Points\n\n';

			localStorage.setItem(module_file_key, default_content);
		}
	},

	ensure_doctype_default_file: function(doctype_name) {
		var doctype_file_key = 'knowlan_doctype_' + doctype_name.replace(/[^a-zA-Z0-9]/g, '_');

		if (!localStorage.getItem(doctype_file_key)) {
			var default_content = '# ' + doctype_name + ' DocType\n\n' +
				'## Overview\n\n' +
				'This is the main documentation file for the ' + doctype_name + ' DocType.\n\n' +
				'## Purpose\n\n' +
				'## Key Fields\n\n' +
				'## Business Rules\n\n' +
				'## Usage Examples\n\n' +
				'## Related DocTypes\n\n';

			localStorage.setItem(doctype_file_key, default_content);
		}
	},

	load_default_file: function(type, name, app_name, module_name) {
		var file_key = '';
		var content = '';

		switch(type) {
			case 'app':
				file_key = 'knowlan_app_' + name.replace(/[^a-zA-Z0-9]/g, '_');
				break;
			case 'module':
				file_key = 'knowlan_module_' + app_name.replace(/[^a-zA-Z0-9]/g, '_') + '_' + name.replace(/[^a-zA-Z0-9]/g, '_');
				break;
			case 'doctype':
				file_key = 'knowlan_doctype_' + name.replace(/[^a-zA-Z0-9]/g, '_');
				break;
		}

		content = localStorage.getItem(file_key) || '';

		// Switch to edit mode and load content
		this.switch_to_edit_mode();
		this.container.find('#notes-textarea').val(content);
		this.generate_table_of_contents();

		// Update current page tracking
		this.current_page = type + '-default';
		this.current_default_file = {
			type: type,
			name: name,
			app_name: app_name,
			module_name: module_name,
			file_key: file_key
		};

		frappe.show_alert({
			message: 'Opened ' + type + ' documentation: ' + name,
			indicator: 'blue'
		}, 2);
	},

	save_default_file: function() {
		if (this.current_default_file) {
			var content = this.container.find('#notes-textarea').val();
			localStorage.setItem(this.current_default_file.file_key, content);
		}
	},

	// Progress Tracking System
	is_field_completed: function(fieldname) {
		if (!this.current_doctype) return false;
		var doctype_name = this.current_doctype.doctype.name;
		var completion_key = 'knowlan_field_completion_' + doctype_name;
		var completions = JSON.parse(localStorage.getItem(completion_key) || '{}');
		return completions[fieldname] || false;
	},

	set_field_completion: function(fieldname, completed) {
		if (!this.current_doctype) return;
		var doctype_name = this.current_doctype.doctype.name;
		var completion_key = 'knowlan_field_completion_' + doctype_name;
		var completions = JSON.parse(localStorage.getItem(completion_key) || '{}');
		completions[fieldname] = completed;
		localStorage.setItem(completion_key, JSON.stringify(completions));
		this.update_progress_display();

		if (completed) {
			// Check if all fields are completed to auto-complete doctype
			this.check_auto_complete_doctype();
		} else {
			// If field is unchecked, uncheck the doctype
			this.set_doctype_completion_status(doctype_name, false);
			this.container.find('.doctype-checkbox[data-name="' + doctype_name + '"]').prop('checked', false);
		}

		// Update select all checkbox
		this.update_select_all_checkbox();
	},

	update_progress_display: function() {
		// Use the enhanced version that includes code tracking
		this.update_progress_display_with_code();
	},

	update_overview_progress_stats: function(completed_fields, total_fields, percentage) {
		// Add progress stats to overview if not already present
		var overview_stats = this.container.find('.overview-stats .row');
		if (overview_stats.length > 0 && overview_stats.find('.progress-stat').length === 0) {
			overview_stats.append(`
				<div class="col-md-3">
					<div class="stat-card progress-stat">
						<div class="stat-value">${percentage}%</div>
						<div class="stat-label">Completion</div>
					</div>
				</div>
				<div class="col-md-3">
					<div class="stat-card progress-stat">
						<div class="stat-value">${completed_fields}/${total_fields}</div>
						<div class="stat-label">Fields Completed</div>
					</div>
				</div>
			`);
		} else {
			// Update existing progress stats
			overview_stats.find('.progress-stat').eq(0).find('.stat-value').text(percentage + '%');
			overview_stats.find('.progress-stat').eq(1).find('.stat-value').text(completed_fields + '/' + total_fields);
		}
	},

	setup_progress_handlers: function() {
		var me = this;

		// Handle individual field completion checkboxes
		this.container.find('.field-completion-checkbox').on('change', function() {
			var fieldname = $(this).data('fieldname');
			var completed = $(this).is(':checked');
			me.set_field_completion(fieldname, completed);
		});

		// Handle select all checkbox
		this.container.find('#select-all-fields').on('change', function() {
			var checked = $(this).is(':checked');
			me.container.find('.field-completion-checkbox').each(function() {
				$(this).prop('checked', checked);
				var fieldname = $(this).data('fieldname');
				me.set_field_completion(fieldname, checked);
			});

			// Also handle code files if enabled
			if (me.current_doctype) {
				var include_code = me.get_code_tracking_preference(me.current_doctype.doctype.name);
				if (include_code) {
					var code_completion_key = 'knowlan_code_completion_' + me.current_doctype.doctype.name;
					var code_completions = JSON.parse(localStorage.getItem(code_completion_key) || '{}');

					// Update all code files
					for (var file in code_completions) {
						code_completions[file] = checked;
					}

					localStorage.setItem(code_completion_key, JSON.stringify(code_completions));

					// Update code files checkboxes
					me.container.find('.code-file-checkbox').prop('checked', checked);
				}

				// Update doctype completion status
				if (checked) {
					me.set_doctype_completion_status(me.current_doctype.doctype.name, true);
					me.container.find('.doctype-checkbox[data-name="' + me.current_doctype.doctype.name + '"]').prop('checked', true);
				} else {
					me.set_doctype_completion_status(me.current_doctype.doctype.name, false);
					me.container.find('.doctype-checkbox[data-name="' + me.current_doctype.doctype.name + '"]').prop('checked', false);
				}
			}
		});

		// Handle include code tracking
		this.container.find('#include-code-tracking').on('change', function() {
			var include_code = $(this).is(':checked');
			me.set_code_tracking_preference(include_code);

			// Show/hide code files summary based on preference
			if (include_code) {
				me.show_code_files_tracking();
			} else {
				me.hide_code_files_tracking();
			}
		});
	},

	set_code_tracking_preference: function(include_code) {
		if (!this.current_doctype) return;
		var doctype_name = this.current_doctype.doctype.name;
		var pref_key = 'knowlan_code_tracking_' + doctype_name;
		localStorage.setItem(pref_key, include_code ? 'true' : 'false');

		// Update progress calculation to include code files
		this.update_progress_display_with_code();
	},

	get_code_tracking_preference: function(doctype_name) {
		var pref_key = 'knowlan_code_tracking_' + doctype_name;
		return localStorage.getItem(pref_key) === 'true';
	},

	is_code_file_completed: function(doctype_name, file_path) {
		var completion_key = 'knowlan_code_completion_' + doctype_name + '_' + file_path.replace(/[^a-zA-Z0-9]/g, '_');
		return localStorage.getItem(completion_key) === 'true';
	},

	set_code_file_completion: function(doctype_name, file_path, completed) {
		var completion_key = 'knowlan_code_completion_' + doctype_name + '_' + file_path.replace(/[^a-zA-Z0-9]/g, '_');
		localStorage.setItem(completion_key, completed ? 'true' : 'false');
		this.update_progress_display_with_code();
	},

	update_progress_display_with_code: function() {
		if (!this.current_doctype) return;

		var doctype_name = this.current_doctype.doctype.name;
		var include_code = this.get_code_tracking_preference(doctype_name);

		var total_fields = this.current_doctype.fields.length;
		var completed_fields = 0;

		// Count completed fields
		this.current_doctype.fields.forEach(field => {
			if (this.is_field_completed(field.fieldname)) {
				completed_fields++;
			}
		});

		var total_items = total_fields;
		var completed_items = completed_fields;

		if (include_code && this.current_doctype.code_files) {
			var total_code_files = this.current_doctype.code_files.length;
			var completed_code_files = 0;

			this.current_doctype.code_files.forEach(file => {
				if (this.is_code_file_completed(doctype_name, file.path)) {
					completed_code_files++;
				}
			});

			total_items += total_code_files;
			completed_items += completed_code_files;

			// Update code files summary
			this.update_code_files_summary(completed_code_files, total_code_files);
		}

		var percentage = total_items > 0 ? Math.round((completed_items / total_items) * 100) : 0;

		// Update progress summary
		this.container.find('.completed-count').text(completed_items);
		this.container.find('.total-count').text(total_items);
		this.container.find('.completion-percentage').text(percentage + '%');

		// Update overview stats
		this.update_overview_progress_stats(completed_items, total_items, percentage);
	},

	update_code_files_summary: function(completed_files, total_files) {
		var summary_html = '<div class="code-files-summary">' +
			'<span class="completed-code-count">' + completed_files + '</span> / ' +
			'<span class="total-code-count">' + total_files + '</span> files completed</div>';

		if (this.container.find('.code-files-summary').length === 0) {
			this.container.find('.progress-summary').after(summary_html);
		} else {
			this.container.find('.code-files-summary').replaceWith(summary_html);
		}
	},

	show_code_files_tracking: function() {
		// Show code files in the code tab with completion checkboxes
		if (this.current_doctype) {
			this.load_doctype_code(this.current_doctype.doctype.name);
		}
	},

	hide_code_files_tracking: function() {
		// Hide code files summary
		this.container.find('.code-files-summary').hide();
	},

	initialize_code_tracking_preference: function() {
		if (!this.current_doctype) return;

		var doctype_name = this.current_doctype.doctype.name;
		var include_code = this.get_code_tracking_preference(doctype_name);

		// Set checkbox state
		this.container.find('#include-code-tracking').prop('checked', include_code);

		// Update display based on preference
		if (include_code) {
			this.show_code_files_tracking();
		} else {
			this.hide_code_files_tracking();
		}
	},

	// Dynamic Tab Visibility System
	current_selection: null,
	selection_history: [],

	hide_all_tabs: function() {
		this.container.find('.main-controls .btn-group').hide();
	},

	show_tabs_for_selection: function(selection_type) {
		var tabs = this.container.find('.main-controls .btn-group');
		tabs.show();

		// Show different tabs based on selection type
		if (selection_type === 'doctype') {
			// Show all tabs for DocType selection
			tabs.find('#view-overview, #view-fields, #view-code, #view-links, #view-notes').show();
		} else if (selection_type === 'module') {
			// Show only overview tab for Module selection
			tabs.find('#view-overview').show();
			tabs.find('#view-fields, #view-code, #view-links, #view-notes').hide();
		} else if (selection_type === 'app') {
			// Show only overview tab for App selection
			tabs.find('#view-overview').show();
			tabs.find('#view-fields, #view-code, #view-links, #view-notes').hide();
		}
	},

	update_selection_state: function(type, data) {
		// Store current selection
		this.current_selection = {
			type: type,
			data: data,
			timestamp: new Date()
		};

		// Add to history (keep last 10)
		this.selection_history.unshift(this.current_selection);
		if (this.selection_history.length > 10) {
			this.selection_history = this.selection_history.slice(0, 10);
		}

		// Update tab visibility
		this.show_tabs_for_selection(type);

		// Store in localStorage for persistence
		localStorage.setItem('knowlan_current_selection', JSON.stringify(this.current_selection));
	},

	restore_selection_state: function() {
		var stored_selection = localStorage.getItem('knowlan_current_selection');
		if (stored_selection) {
			try {
				this.current_selection = JSON.parse(stored_selection);
				if (this.current_selection) {
					this.show_tabs_for_selection(this.current_selection.type);
				}
			} catch (e) {
				console.warn('Failed to restore selection state:', e);
			}
		}
	},

	clear_selection_state: function() {
		this.current_selection = null;
		this.hide_all_tabs();
		localStorage.removeItem('knowlan_current_selection');
	},

	get_selection_breadcrumb: function() {
		if (!this.current_selection) return [];

		var breadcrumb = [];
		var data = this.current_selection.data;

		if (this.current_selection.type === 'doctype') {
			breadcrumb = [data.app_name, data.module_name, data.doctype_name];
		} else if (this.current_selection.type === 'module') {
			breadcrumb = [data.app_name, data.module_name];
		} else if (this.current_selection.type === 'app') {
			breadcrumb = [data.app_name];
		}

		return breadcrumb;
	},

	update_tab_visibility: function() {
		if (this.current_doctype) {
			this.update_selection_state('doctype', {
				app_name: this.current_doctype.app_name,
				module_name: this.current_doctype.doctype.module,
				doctype_name: this.current_doctype.doctype.name
			});
		} else if (this.current_selection) {
			this.show_tabs_for_selection(this.current_selection.type);
		} else {
			this.hide_all_tabs();
		}
	},

	// App and Module Overview Pages
	render_app_overview: function(app_name) {
		var me = this;

		// Get app details from the loaded apps data
		var app_data = null;
		if (this.apps_data) {
			app_data = this.apps_data.find(app => app.name === app_name);
		}

		if (app_data) {
			this.display_app_overview(app_data);
		} else {
			// Try API call as fallback
			frappe.call({
				method: 'knowlan.api.v1.metadata.get_app_details',
				args: { app_name: app_name },
				callback: function(r) {
					if (r.message) {
						me.display_app_overview(r.message);
					} else {
						me.display_basic_app_overview(app_name);
					}
				},
				error: function() {
					me.display_basic_app_overview(app_name);
				}
			});
		}
	},

	display_app_overview: function(app_data) {
		var overview_html = `
			<div class="app-overview">
				<div class="overview-header">
					<h3>${app_data.name}</h3>
					<div class="description-section">
						<p>${app_data.description || 'No description available'}</p>
					</div>
				</div>
				<div class="overview-stats">
					<div class="row">
						<div class="col-md-3">
							<div class="stat-card">
								<div class="stat-value">${app_data.modules ? app_data.modules.length : 0}</div>
								<div class="stat-label">Modules</div>
							</div>
						</div>
						<div class="col-md-3">
							<div class="stat-card">
								<div class="stat-value">${app_data.doctype_count || 0}</div>
								<div class="stat-label">DocTypes</div>
							</div>
						</div>
						<div class="col-md-3">
							<div class="stat-card">
								<div class="stat-value">${app_data.version || 'N/A'}</div>
								<div class="stat-label">Version</div>
							</div>
						</div>
						<div class="col-md-3">
							<div class="stat-card clickable-stat" data-action="open-app" style="cursor: pointer;">
								<div class="stat-value"><i class="fa fa-external-link"></i></div>
								<div class="stat-label">Open App</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modules-summary">
					<h4><i class="fa fa-folder-o"></i> Modules</h4>
					<div class="summary-container">
						${this.render_modules_summary(app_data.modules || [])}
					</div>
				</div>
			</div>
		`;

		this.container.find('#overview-panel').html(overview_html);

		// Setup click handler for open app
		var me = this;
		this.container.find('[data-action="open-app"]').on('click', function() {
			me.open_app_definition(app_data.name);
		});

		// Setup module click handlers
		this.setup_module_summary_handlers(app_data.name);
	},

	display_basic_app_overview: function(app_name) {
		var overview_html = `
			<div class="app-overview">
				<div class="overview-header">
					<h3>${app_name}</h3>
					<div class="description-section">
						<p>Application overview for ${app_name}</p>
					</div>
				</div>
				<div class="overview-stats">
					<div class="row">
						<div class="col-md-4">
							<div class="stat-card clickable-stat" data-action="open-app" style="cursor: pointer;">
								<div class="stat-value"><i class="fa fa-external-link"></i></div>
								<div class="stat-label">Open App</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		`;

		this.container.find('#overview-panel').html(overview_html);

		// Setup click handler for open app
		var me = this;
		this.container.find('[data-action="open-app"]').on('click', function() {
			me.open_app_definition(app_name);
		});
	},

	render_module_overview: function(app_name, module_name) {
		var me = this;

		// Get module details from loaded apps data
		var module_data = null;
		if (this.apps_data) {
			var app_data = this.apps_data.find(app => app.name === app_name);
			if (app_data && app_data.modules) {
				var modules = {};
				app_data.doctypes.forEach(function(doctype) {
					if (!modules[doctype.module]) {
						modules[doctype.module] = [];
					}
					modules[doctype.module].push(doctype);
				});

				if (modules[module_name]) {
					module_data = {
						name: module_name,
						app_name: app_name,
						doctypes: modules[module_name],
						doctype_count: modules[module_name].length
					};
				}
			}
		}

		if (module_data) {
			this.display_module_overview(module_data);
		} else {
			// Try API call as fallback
			frappe.call({
				method: 'knowlan.api.v1.metadata.get_module_details',
				args: {
					app_name: app_name,
					module_name: module_name
				},
				callback: function(r) {
					if (r.message) {
						me.display_module_overview(r.message);
					} else {
						me.display_basic_module_overview(app_name, module_name);
					}
				},
				error: function() {
					me.display_basic_module_overview(app_name, module_name);
				}
			});
		}
	},

	display_module_overview: function(module_data) {
		var overview_html = `
			<div class="module-overview">
				<div class="overview-header">
					<h3>${module_data.name}</h3>
					<div class="description-section">
						<p><strong>App:</strong> ${module_data.app_name}</p>
						<p>${module_data.description || 'No description available'}</p>
					</div>
				</div>
				<div class="overview-stats">
					<div class="row">
						<div class="col-md-4">
							<div class="stat-card">
								<div class="stat-value">${module_data.doctype_count || 0}</div>
								<div class="stat-label">DocTypes</div>
							</div>
						</div>
						<div class="col-md-4">
							<div class="stat-card clickable-stat" data-action="open-module" style="cursor: pointer;">
								<div class="stat-value"><i class="fa fa-external-link"></i></div>
								<div class="stat-label">Open Module</div>
							</div>
						</div>
					</div>
				</div>
				<div class="doctypes-summary">
					<h4><i class="fa fa-file-text-o"></i> DocTypes</h4>
					<div class="summary-container">
						${this.render_doctypes_summary(module_data.doctypes || [], module_data.name, module_data.app_name)}
					</div>
				</div>
			</div>
		`;

		this.container.find('#overview-panel').html(overview_html);

		// Setup click handler for open module
		var me = this;
		this.container.find('[data-action="open-module"]').on('click', function() {
			me.open_module_definition(module_data.app_name, module_data.name);
		});

		// Setup doctype click handlers
		this.setup_doctype_summary_handlers();
	},

	display_basic_module_overview: function(app_name, module_name) {
		var me = this;

		// Try to get doctypes for this module
		frappe.call({
			method: 'knowlan.api.v1.metadata.get_doctypes_by_module',
			args: {
				app_name: app_name,
				module_name: module_name
			},
			callback: function(r) {
				var doctypes = r.message || [];
				var overview_html = `
					<div class="module-overview">
						<div class="overview-header">
							<h3>${module_name}</h3>
							<div class="description-section">
								<p><strong>App:</strong> ${app_name}</p>
								<p>Module overview for ${module_name}</p>
							</div>
						</div>
						<div class="overview-stats">
							<div class="row">
								<div class="col-md-4">
									<div class="stat-card">
										<div class="stat-value">${doctypes.length}</div>
										<div class="stat-label">DocTypes</div>
									</div>
								</div>
								<div class="col-md-4">
									<div class="stat-card clickable-stat" data-action="open-module" style="cursor: pointer;">
										<div class="stat-value"><i class="fa fa-external-link"></i></div>
										<div class="stat-label">Open Module</div>
									</div>
								</div>
							</div>
						</div>
						<div class="doctypes-summary">
							<h4><i class="fa fa-file-text-o"></i> DocTypes</h4>
							<div class="summary-container">
								${me.render_doctypes_summary(doctypes, module_name, app_name)}
							</div>
						</div>
					</div>
				`;

				me.container.find('#overview-panel').html(overview_html);

				// Setup click handler for open module
				me.container.find('[data-action="open-module"]').on('click', function() {
					me.open_module_definition(app_name, module_name);
				});

				// Setup doctype click handlers
				me.setup_doctype_summary_handlers();
			},
			error: function() {
				// Fallback to basic display
				me.display_basic_module_fallback(app_name, module_name);
			}
		});
	},

	display_basic_module_fallback: function(app_name, module_name) {
		var overview_html = `
			<div class="module-overview">
				<div class="overview-header">
					<h3>${module_name}</h3>
					<div class="description-section">
						<p><strong>App:</strong> ${app_name}</p>
						<p>Module overview for ${module_name}</p>
					</div>
				</div>
				<div class="overview-stats">
					<div class="row">
						<div class="col-md-4">
							<div class="stat-card clickable-stat" data-action="open-module" style="cursor: pointer;">
								<div class="stat-value"><i class="fa fa-external-link"></i></div>
								<div class="stat-label">Open Module</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		`;

		this.container.find('#overview-panel').html(overview_html);

		// Setup click handler for open module
		var me = this;
		this.container.find('[data-action="open-module"]').on('click', function() {
			me.open_module_definition(app_name, module_name);
		});
	},

	open_app_definition: function(app_name) {
		// Open app in new tab/window
		var app_url = '/app/' + app_name.toLowerCase().replace(/\s+/g, '-');
		window.open(app_url, '_blank');

		frappe.show_alert({
			message: 'Opening ' + app_name + ' app',
			indicator: 'blue'
		}, 2);
	},

	open_module_definition: function(app_name, module_name) {
		// Open module in new tab/window
		var module_url = '/app/' + app_name.toLowerCase().replace(/\s+/g, '-') + '/' + module_name.toLowerCase().replace(/\s+/g, '-');
		window.open(module_url, '_blank');

		frappe.show_alert({
			message: 'Opening ' + module_name + ' module',
			indicator: 'blue'
		}, 2);
	},

	open_doctype_customize: function(doctype_name) {
		// Open customize form for the doctype with proper URL structure
		var customize_url = 'http://knowlan-site:8010/app/customize-form/Customize%20Form#form_tab';

		// Open the customize form and set the doctype
		var customize_window = window.open(customize_url, '_blank');

		// Set the doctype after the page loads
		setTimeout(function() {
			try {
				if (customize_window && !customize_window.closed) {
					// Try to set the doctype in the customize form
					customize_window.postMessage({
						action: 'set_doctype',
						doctype: doctype_name
					}, '*');
				}
			} catch (e) {
				console.log('Could not set doctype automatically:', e);
			}
		}, 2000);

		frappe.show_alert({
			message: 'Opening customize form for ' + doctype_name + '. Please select the DocType if not auto-selected.',
			indicator: 'blue'
		}, 4);
	},

	// DocType Links Functionality
	load_doctype_links: function(doctype_name) {
		var me = this;

		frappe.call({
			method: 'knowlan.api.v1.metadata.get_doctype_links',
			args: { doctype: doctype_name },
			callback: function(r) {
				if (r.message) {
					me.render_doctype_links(r.message);
				} else {
					me.render_empty_links();
				}
			},
			error: function() {
				me.render_empty_links();
			}
		});
	},

	render_doctype_links: function(links_data) {
		var me = this;

		// Add legend first
		var legend_html = `
			<div class="links-legend">
				<div class="legend-item">
					<div class="legend-color outgoing-color"></div>
					<span>Outgoing: This DocType links to others</span>
				</div>
				<div class="legend-item">
					<div class="legend-color incoming-color"></div>
					<span>Incoming: Other DocTypes link to this one</span>
				</div>
			</div>
		`;

		this.container.find('.links-content').prepend(legend_html);

		var outgoing_html = '';
		var incoming_html = '';

		// Render outgoing links
		if (links_data.outgoing && links_data.outgoing.length > 0) {
			links_data.outgoing.forEach(link => {
				outgoing_html += `
					<div class="link-item-container outgoing-link" data-doctype="${link.doctype}" data-fieldname="${link.fieldname}" data-fieldtype="${link.fieldtype}">
						<div class="link-item-header">
							<div class="link-icon">
								<i class="fa fa-arrow-right"></i>
							</div>
							<div class="link-info">
								<div class="link-doctype-name">${link.doctype}</div>
								<div class="link-field-info">
									<span class="field-name">${link.fieldname}</span>
									<span class="field-type">${link.fieldtype}</span>
								</div>
							</div>
						</div>
						<div class="link-actions">
							<button class="btn btn-xs btn-primary view-link" data-doctype="${link.doctype}">
								<i class="fa fa-eye"></i> View
							</button>
						</div>
					</div>
				`;
			});
		} else {
			outgoing_html = '<div class="empty-links-container"><i class="fa fa-info-circle"></i> No outgoing links found</div>';
		}

		// Render incoming links
		if (links_data.incoming && links_data.incoming.length > 0) {
			links_data.incoming.forEach(link => {
				incoming_html += `
					<div class="link-item-container incoming-link" data-doctype="${link.doctype}" data-fieldname="${link.fieldname}" data-fieldtype="${link.fieldtype}">
						<div class="link-item-header">
							<div class="link-icon">
								<i class="fa fa-arrow-left"></i>
							</div>
							<div class="link-info">
								<div class="link-doctype-name">${link.doctype}</div>
								<div class="link-field-info">
									<span class="field-name">${link.fieldname}</span>
									<span class="field-type">${link.fieldtype}</span>
								</div>
							</div>
						</div>
						<div class="link-actions">
							<button class="btn btn-xs btn-primary view-link" data-doctype="${link.doctype}">
								<i class="fa fa-eye"></i> View
							</button>
						</div>
					</div>
				`;
			});
		} else {
			incoming_html = '<div class="empty-links-container"><i class="fa fa-info-circle"></i> No incoming links found</div>';
		}

		this.container.find('#outgoing-links').html(outgoing_html);
		this.container.find('#incoming-links').html(incoming_html);

		// Setup link handlers
		this.setup_link_handlers();
	},

	render_empty_links: function() {
		var empty_html = '<div class="empty-links">No links data available</div>';
		this.container.find('#outgoing-links').html(empty_html);
		this.container.find('#incoming-links').html(empty_html);
	},

	setup_link_handlers: function() {
		var me = this;

		// Use event delegation for dynamically added elements
		this.container.off('click.linkhandler').on('click.linkhandler', '.view-link', function() {
			var doctype_name = $(this).data('doctype');
			me.select_doctype(doctype_name);
		});

		// Handle link item selection
		this.container.off('click.linkselect').on('click.linkselect', '.link-item', function(e) {
			if (!$(e.target).hasClass('btn')) {
				// Remove previous selection
				me.container.find('.link-item').removeClass('selected');
				$(this).addClass('selected');

				// Show link details modal
				var doctype_name = $(this).data('doctype');
				var fieldname = $(this).data('fieldname');
				var fieldtype = $(this).data('fieldtype');
				var link_type = $(this).hasClass('outgoing-link') ? 'outgoing' : 'incoming';

				me.show_link_details_modal(doctype_name, fieldname, fieldtype, link_type);
			}
		});
	},

	// Summary Container Rendering
	render_modules_summary: function(modules) {
		if (!modules || modules.length === 0) {
			return '<div class="empty-summary">No modules found</div>';
		}

		var summary_html = '<div class="summary-grid">';

		modules.forEach(module => {
			var completion_status = this.get_module_completion_status(module.app_name, module.name);
			var completion_class = completion_status ? 'completed' : 'incomplete';

			summary_html += `
				<div class="summary-item module-summary ${completion_class}" data-module="${module.name}" data-app="${module.app_name}">
					<div class="summary-header">
						<i class="fa fa-folder-o"></i>
						<span class="summary-title">${module.name}</span>
						<span class="completion-indicator ${completion_class}">
							<i class="fa ${completion_status ? 'fa-check-circle' : 'fa-circle-o'}"></i>
						</span>
					</div>
					<div class="summary-stats">
						<span class="stat-item">
							<i class="fa fa-file-text-o"></i>
							${module.doctype_count || 0} DocTypes
						</span>
					</div>
					<div class="summary-actions">
						<button class="btn btn-xs btn-primary open-module" data-module="${module.name}" data-app="${module.app_name}">
							<i class="fa fa-eye"></i> View
						</button>
					</div>
				</div>
			`;
		});

		summary_html += '</div>';
		return summary_html;
	},

	render_doctypes_summary: function(doctypes, module_name, app_name) {
		if (!doctypes || doctypes.length === 0) {
			return '<div class="empty-summary">No doctypes found</div>';
		}

		var summary_html = '<div class="summary-grid">';

		doctypes.forEach(doctype => {
			var completion_status = this.get_doctype_completion_status(doctype.name);
			var completion_class = completion_status ? 'completed' : 'incomplete';
			var icon = this.get_doctype_icon(doctype);

			summary_html += `
				<div class="summary-item doctype-summary ${completion_class}" data-doctype="${doctype.name}">
					<div class="summary-header">
						<i class="${icon}"></i>
						<span class="summary-title">${doctype.name}</span>
						<span class="completion-indicator ${completion_class}">
							<i class="fa ${completion_status ? 'fa-check-circle' : 'fa-circle-o'}"></i>
						</span>
					</div>
					<div class="summary-stats">
						<span class="stat-item">
							<i class="fa fa-list"></i>
							${doctype.fields ? doctype.fields.length : 0} Fields
						</span>
					</div>
					<div class="summary-actions">
						<button class="btn btn-xs btn-primary open-doctype" data-doctype="${doctype.name}">
							<i class="fa fa-eye"></i> View
						</button>
					</div>
				</div>
			`;
		});

		summary_html += '</div>';
		return summary_html;
	},

	setup_module_summary_handlers: function(app_name) {
		var me = this;

		this.container.find('.open-module').on('click', function() {
			var module_name = $(this).data('module');
			var app_name = $(this).data('app');
			me.show_module_overview(app_name, module_name);
		});
	},

	setup_doctype_summary_handlers: function() {
		var me = this;

		this.container.find('.open-doctype').on('click', function() {
			var doctype_name = $(this).data('doctype');
			me.load_doctype(doctype_name);
		});

		// Add hover effects for summary items
		this.container.find('.summary-item').on('mouseenter', function() {
			$(this).addClass('summary-hover');
		}).on('mouseleave', function() {
			$(this).removeClass('summary-hover');
		});

		// Add click handlers for summary items (not just buttons)
		this.container.find('.doctype-summary').on('click', function(e) {
			if (!$(e.target).hasClass('btn')) {
				var doctype_name = $(this).data('doctype');
				me.load_doctype(doctype_name);
			}
		});

		this.container.find('.module-summary').on('click', function(e) {
			if (!$(e.target).hasClass('btn')) {
				var module_name = $(this).data('module');
				var app_name = $(this).data('app');
				me.show_module_overview(app_name, module_name);
			}
		});
	},

	// Enhanced summary rendering with more details
	render_enhanced_modules_summary: function(modules) {
		if (!modules || modules.length === 0) {
			return '<div class="empty-summary"><i class="fa fa-info-circle"></i> No modules found</div>';
		}

		var summary_html = '<div class="summary-grid enhanced-grid">';

		modules.forEach(module => {
			var completion_status = this.get_module_completion_status(module.app_name, module.name);
			var completion_class = completion_status ? 'completed' : 'incomplete';
			var progress_percentage = this.calculate_module_progress(module.name, module.app_name);

			summary_html += `
				<div class="summary-item module-summary enhanced-summary ${completion_class}" data-module="${module.name}" data-app="${module.app_name}">
					<div class="summary-header">
						<i class="fa fa-folder-o"></i>
						<span class="summary-title">${module.name}</span>
						<span class="completion-indicator ${completion_class}">
							<i class="fa ${completion_status ? 'fa-check-circle' : 'fa-circle-o'}"></i>
						</span>
					</div>
					<div class="summary-description">
						<small class="text-muted">${module.description || 'No description available'}</small>
					</div>
					<div class="summary-stats">
						<span class="stat-item">
							<i class="fa fa-file-text-o"></i>
							${module.doctype_count || 0} DocTypes
						</span>
						<span class="stat-item">
							<i class="fa fa-percent"></i>
							${progress_percentage}% Complete
						</span>
					</div>
					<div class="summary-actions">
						<button class="btn btn-xs btn-primary open-module" data-module="${module.name}" data-app="${module.app_name}">
							<i class="fa fa-eye"></i> View
						</button>
						<button class="btn btn-xs btn-outline-secondary toggle-completion" data-type="module" data-name="${module.name}" data-app="${module.app_name}">
							<i class="fa ${completion_status ? 'fa-undo' : 'fa-check'}"></i>
							${completion_status ? 'Undo' : 'Complete'}
						</button>
					</div>
				</div>
			`;
		});

		summary_html += '</div>';
		return summary_html;
	},

	calculate_module_progress: function(module_name, app_name) {
		// Calculate progress based on completed doctypes in the module
		// This is a placeholder - would need actual doctype data
		return Math.floor(Math.random() * 100); // Placeholder
	},

	calculate_doctype_progress: function(doctype_name) {
		// Calculate progress based on completed fields
		if (!this.current_doctype || this.current_doctype.doctype.name !== doctype_name) {
			return 0; // Can't calculate without field data
		}

		var total_fields = this.current_doctype.fields.length;
		var completed_fields = 0;

		this.current_doctype.fields.forEach(field => {
			if (this.is_field_completed(field.fieldname)) {
				completed_fields++;
			}
		});

		return total_fields > 0 ? Math.round((completed_fields / total_fields) * 100) : 0;
	},

	// Live Preview System
	update_live_preview: function() {
		if (this.notes_mode !== 'split') return;

		var content = this.container.find('#notes-textarea').val();
		var preview_panel = this.container.find('#live-preview');

		if (preview_panel.length === 0) return;

		// Render markdown with Obsidian links
		var rendered_content = this.render_markdown_content(content);
		rendered_content = this.render_obsidian_links(rendered_content);

		preview_panel.html(rendered_content);

		// Setup link handlers
		this.setup_obsidian_link_handlers();
	},

	// Manual Completion Toggle System
	get_app_completion_status: function(app_name) {
		var completion_key = 'knowlan_app_completion_' + app_name.replace(/[^a-zA-Z0-9]/g, '_');
		return localStorage.getItem(completion_key) === 'true';
	},

	set_app_completion_status: function(app_name, completed) {
		var completion_key = 'knowlan_app_completion_' + app_name.replace(/[^a-zA-Z0-9]/g, '_');
		localStorage.setItem(completion_key, completed ? 'true' : 'false');
	},

	get_module_completion_status: function(app_name, module_name) {
		var completion_key = 'knowlan_module_completion_' + app_name.replace(/[^a-zA-Z0-9]/g, '_') + '_' + module_name.replace(/[^a-zA-Z0-9]/g, '_');
		return localStorage.getItem(completion_key) === 'true';
	},

	set_module_completion_status: function(app_name, module_name, completed) {
		var completion_key = 'knowlan_module_completion_' + app_name.replace(/[^a-zA-Z0-9]/g, '_') + '_' + module_name.replace(/[^a-zA-Z0-9]/g, '_');
		localStorage.setItem(completion_key, completed ? 'true' : 'false');
	},

	get_doctype_completion_status: function(doctype_name) {
		var completion_key = 'knowlan_doctype_completion_' + doctype_name.replace(/[^a-zA-Z0-9]/g, '_');
		return localStorage.getItem(completion_key) === 'true';
	},

	set_doctype_completion_status: function(doctype_name, completed) {
		var completion_key = 'knowlan_doctype_completion_' + doctype_name.replace(/[^a-zA-Z0-9]/g, '_');
		localStorage.setItem(completion_key, completed ? 'true' : 'false');
	},

	is_doctype_completed: function(doctype_name) {
		var completion_key = 'knowlan_doctype_completion_' + doctype_name.replace(/[^a-zA-Z0-9]/g, '_');
		return localStorage.getItem(completion_key) === 'true';
	},

	set_module_completion_status: function(module_name, app_name, completed) {
		var completion_key = 'knowlan_module_completion_' + app_name.replace(/[^a-zA-Z0-9]/g, '_') + '_' + module_name.replace(/[^a-zA-Z0-9]/g, '_');
		localStorage.setItem(completion_key, completed ? 'true' : 'false');
	},

	is_module_completed: function(module_name, app_name) {
		var completion_key = 'knowlan_module_completion_' + app_name.replace(/[^a-zA-Z0-9]/g, '_') + '_' + module_name.replace(/[^a-zA-Z0-9]/g, '_');
		return localStorage.getItem(completion_key) === 'true';
	},

	set_app_completion_status: function(app_name, completed) {
		var completion_key = 'knowlan_app_completion_' + app_name.replace(/[^a-zA-Z0-9]/g, '_');
		localStorage.setItem(completion_key, completed ? 'true' : 'false');
	},

	is_app_completed: function(app_name) {
		var completion_key = 'knowlan_app_completion_' + app_name.replace(/[^a-zA-Z0-9]/g, '_');
		return localStorage.getItem(completion_key) === 'true';
	},

	setup_completion_handlers: function() {
		var me = this;

		// Handle completion checkbox changes
		this.container.find('.completion-checkbox').on('change', function(e) {
			e.stopPropagation(); // Prevent node expansion

			var type = $(this).data('type');
			var name = $(this).data('name');
			var completed = $(this).is(':checked');

			if (type === 'app') {
				if (completed) {
					// Handle app manual completion
					me.handle_app_manual_completion(name);
				} else {
					me.set_app_completion_status(name, false);
				}
			} else if (type === 'module') {
				var app_name = $(this).data('app');
				if (completed) {
					// Handle module manual completion
					me.handle_module_manual_completion(name, app_name);
				} else {
					me.set_module_completion_status(name, app_name, false);
				}
			} else if (type === 'doctype') {
				if (completed) {
					// Handle doctype manual completion
					me.handle_doctype_manual_completion(name);
				} else {
					me.set_doctype_completion_status(name, false);
				}
			}

			frappe.show_alert({
				message: type.charAt(0).toUpperCase() + type.slice(1) + ' "' + name + '" marked as ' + (completed ? 'completed' : 'incomplete'),
				indicator: completed ? 'green' : 'orange'
			}, 2);
		});
	},

	// Hierarchical Completion System
	check_auto_complete_doctype: function() {
		if (!this.current_doctype) return;

		var total_fields = this.current_doctype.fields.length;
		var completed_fields = 0;

		// Count completed fields
		this.current_doctype.fields.forEach(field => {
			if (this.is_field_completed(field.fieldname)) {
				completed_fields++;
			}
		});

		// Get code files completion status if enabled
		var include_code = this.get_code_tracking_preference(this.current_doctype.doctype.name);
		var code_files_completed = 0;
		var total_code_files = 0;

		if (include_code) {
			var code_completion_key = 'knowlan_code_completion_' + this.current_doctype.doctype.name;
			var code_completions = JSON.parse(localStorage.getItem(code_completion_key) || '{}');

			// Count completed code files
			for (var file in code_completions) {
				if (code_completions[file]) {
					code_files_completed++;
				}
				total_code_files++;
			}
		}

		// Calculate total completion status
		var all_completed = false;

		if (include_code && total_code_files > 0) {
			// Both fields and code files must be completed
			all_completed = (completed_fields === total_fields && code_files_completed === total_code_files);
		} else {
			// Only fields need to be completed
			all_completed = (completed_fields === total_fields && total_fields > 0);
		}

		// Auto-complete doctype if everything is completed
		if (all_completed) {
			var doctype_name = this.current_doctype.doctype.name;
			this.set_doctype_completion_status(doctype_name, true);

			// Update tree view checkbox
			this.container.find('.doctype-checkbox[data-name="' + doctype_name + '"]').prop('checked', true);

			frappe.show_alert({
				message: 'DocType "' + doctype_name + '" automatically completed!',
				indicator: 'green'
			}, 3);

			// Check if all doctypes in module are completed
			this.check_auto_complete_module(this.current_doctype.doctype.module, this.current_doctype.app_name);
		}
	},

	handle_doctype_manual_completion: function(doctype_name) {
		var me = this;

		frappe.confirm(
			'Do you want to mark all fields in "' + doctype_name + '" as completed?',
			function() {
				// Mark all fields as completed
				if (me.current_doctype && me.current_doctype.doctype.name === doctype_name) {
					me.current_doctype.fields.forEach(field => {
						me.set_field_completion(field.fieldname, true);
						me.container.find('.field-completion-checkbox[data-fieldname="' + field.fieldname + '"]').prop('checked', true);
					});

					// Also mark all code files as completed if enabled
					var include_code = me.get_code_tracking_preference(doctype_name);
					if (include_code) {
						var code_completion_key = 'knowlan_code_completion_' + doctype_name;
						var code_completions = JSON.parse(localStorage.getItem(code_completion_key) || '{}');

						// Mark all code files as completed
						for (var file in code_completions) {
							code_completions[file] = true;
						}

						localStorage.setItem(code_completion_key, JSON.stringify(code_completions));

						// Update code files checkboxes
						me.container.find('.code-file-checkbox').prop('checked', true);
					}

					me.update_progress_display();
					me.update_select_all_checkbox();

					frappe.show_alert({
						message: 'All fields in "' + doctype_name + '" marked as completed',
						indicator: 'green'
					}, 3);

					// Check if all doctypes in module are completed
					me.check_auto_complete_module(me.current_doctype.doctype.module, me.current_doctype.app_name);
				}
			},
			function() {
				// User cancelled, uncheck the doctype
				me.set_doctype_completion_status(doctype_name, false);
				me.container.find('.doctype-checkbox[data-name="' + doctype_name + '"]').prop('checked', false);
			}
		);
	},

	check_auto_complete_module: function(module_name, app_name) {
		if (!module_name || !app_name) return;

		var me = this;
		var all_doctypes_completed = true;

		// Get all doctypes in this module
		var module_doctypes = [];
		if (this.apps_data) {
			this.apps_data.forEach(function(app) {
				if (app.name === app_name) {
					app.doctypes.forEach(function(doctype) {
						if (doctype.module === module_name) {
							module_doctypes.push(doctype.name);
						}
					});
				}
			});
		}

		// Check if all doctypes are completed
		module_doctypes.forEach(function(doctype_name) {
			if (!me.is_doctype_completed(doctype_name)) {
				all_doctypes_completed = false;
			}
		});

		// Auto-complete module if all doctypes are completed
		if (all_doctypes_completed && module_doctypes.length > 0) {
			this.set_module_completion_status(module_name, app_name, true);

			// Update tree view checkbox
			this.container.find('.module-checkbox[data-name="' + module_name + '"][data-app="' + app_name + '"]').prop('checked', true);

			frappe.show_alert({
				message: 'Module "' + module_name + '" automatically completed!',
				indicator: 'green'
			}, 3);

			// Check if all modules in app are completed
			this.check_auto_complete_app(app_name);
		}
	},

	handle_module_manual_completion: function(module_name, app_name) {
		var me = this;

		frappe.confirm(
			'Do you want to mark all DocTypes in module "' + module_name + '" as completed?',
			function() {
				// Get all doctypes in this module
				var module_doctypes = [];
				if (me.apps_data) {
					me.apps_data.forEach(function(app) {
						if (app.name === app_name) {
							app.doctypes.forEach(function(doctype) {
								if (doctype.module === module_name) {
									module_doctypes.push(doctype.name);
								}
							});
						}
					});
				}

				// Mark all doctypes as completed
				module_doctypes.forEach(function(doctype_name) {
					me.set_doctype_completion_status(doctype_name, true);
					me.container.find('.doctype-checkbox[data-name="' + doctype_name + '"]').prop('checked', true);
				});

				// Mark module as completed
				me.set_module_completion_status(module_name, app_name, true);

				frappe.show_alert({
					message: 'All DocTypes in module "' + module_name + '" marked as completed',
					indicator: 'green'
				}, 3);

				// Check if all modules in app are completed
				me.check_auto_complete_app(app_name);
			},
			function() {
				// User cancelled, uncheck the module
				me.set_module_completion_status(module_name, app_name, false);
				me.container.find('.module-checkbox[data-name="' + module_name + '"][data-app="' + app_name + '"]').prop('checked', false);
			}
		);
	},

	check_auto_complete_app: function(app_name) {
		if (!app_name) return;

		var me = this;
		var all_modules_completed = true;

		// Get all modules in this app
		var app_modules = [];
		if (this.apps_data) {
			this.apps_data.forEach(function(app) {
				if (app.name === app_name) {
					app.doctypes.forEach(function(doctype) {
						if (!app_modules.includes(doctype.module)) {
							app_modules.push(doctype.module);
						}
					});
				}
			});
		}

		// Check if all modules are completed
		app_modules.forEach(function(module_name) {
			if (!me.is_module_completed(module_name, app_name)) {
				all_modules_completed = false;
			}
		});

		// Auto-complete app if all modules are completed
		if (all_modules_completed && app_modules.length > 0) {
			this.set_app_completion_status(app_name, true);

			// Update tree view checkbox
			this.container.find('.app-checkbox[data-name="' + app_name + '"]').prop('checked', true);

			frappe.show_alert({
				message: 'App "' + app_name + '" automatically completed!',
				indicator: 'green'
			}, 3);
		}
	},

	handle_app_manual_completion: function(app_name) {
		var me = this;

		frappe.confirm(
			'Do you want to mark all Modules in app "' + app_name + '" as completed?',
			function() {
				// Get all modules in this app
				var app_modules = [];
				if (me.apps_data) {
					me.apps_data.forEach(function(app) {
						if (app.name === app_name) {
							app.doctypes.forEach(function(doctype) {
								if (!app_modules.includes(doctype.module)) {
									app_modules.push(doctype.module);
								}
							});
						}
					});
				}

				// Mark all modules as completed
				app_modules.forEach(function(module_name) {
					me.set_module_completion_status(module_name, app_name, true);
					me.container.find('.module-checkbox[data-name="' + module_name + '"][data-app="' + app_name + '"]').prop('checked', true);

					// Get all doctypes in this module
					var module_doctypes = [];
					if (me.apps_data) {
						me.apps_data.forEach(function(app) {
							if (app.name === app_name) {
								app.doctypes.forEach(function(doctype) {
									if (doctype.module === module_name) {
										module_doctypes.push(doctype.name);
									}
								});
							}
						});
					}

					// Mark all doctypes as completed
					module_doctypes.forEach(function(doctype_name) {
						me.set_doctype_completion_status(doctype_name, true);
						me.container.find('.doctype-checkbox[data-name="' + doctype_name + '"]').prop('checked', true);
					});
				});

				// Mark app as completed
				me.set_app_completion_status(app_name, true);

				frappe.show_alert({
					message: 'All Modules in app "' + app_name + '" marked as completed',
					indicator: 'green'
				}, 3);
			},
			function() {
				// User cancelled, uncheck the app
				me.set_app_completion_status(app_name, false);
				me.container.find('.app-checkbox[data-name="' + app_name + '"]').prop('checked', false);
			}
		);
	},

	update_select_all_checkbox: function() {
		if (!this.current_doctype) return;

		var doctype_name = this.current_doctype.doctype.name;
		var include_code = this.get_code_tracking_preference(doctype_name);

		var total_fields = this.current_doctype.fields.length;
		var completed_fields = 0;

		this.current_doctype.fields.forEach(field => {
			if (this.is_field_completed(field.fieldname)) {
				completed_fields++;
			}
		});

		var total_items = total_fields;
		var completed_items = completed_fields;

		// Include code files if enabled
		if (include_code && this.current_doctype.code_files) {
			var total_code_files = this.current_doctype.code_files.length;
			var completed_code_files = 0;

			this.current_doctype.code_files.forEach(file => {
				if (this.is_code_file_completed(doctype_name, file.path)) {
					completed_code_files++;
				}
			});

			total_items += total_code_files;
			completed_items += completed_code_files;
		}

		var select_all_checkbox = this.container.find('#select-all-fields');
		if (completed_items === 0) {
			select_all_checkbox.prop('checked', false).prop('indeterminate', false);
		} else if (completed_items === total_items) {
			select_all_checkbox.prop('checked', true).prop('indeterminate', false);
		} else {
			select_all_checkbox.prop('checked', false).prop('indeterminate', true);
		}
	}
};

// Learning Classroom module loaded successfully
