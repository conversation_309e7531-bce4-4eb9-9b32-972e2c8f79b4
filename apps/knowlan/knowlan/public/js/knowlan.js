// Copyright (c) 2024, Frappe Technologies and contributors
// License: MIT. See LICENSE

frappe.provide("knowlan");

// Initialize Knowlan when the app is ready
$(document).on("app_ready", function() {
	knowlan.init();
});

// Extend knowlan object instead of overwriting it
$.extend(knowlan, {
	init: function() {
		this.setup_toolbar_button();
		this.setup_page();
		this.setup_accessibility();
		this.setup_responsive_handlers();
	},

	setup_toolbar_button: function() {
		// Add Knowlan button to the toolbar
		var me = this;

		// Wait for toolbar to be ready
		setTimeout(function() {
			if (frappe.ui && frappe.ui.toolbar) {
				// Add button to navbar
				var $navbar = $('.navbar-nav');
				if ($navbar.length && !$navbar.find('.knowlan-nav-button').length) {
					var $knowlan_button = $(`
						<li class="nav-item knowlan-nav-button">
							<a class="nav-link" href="#" onclick="knowlan.show(); return false;">
								<i class="fa fa-sitemap"></i>
								<span class="hidden-xs"> ${__("Knowlan")}</span>
							</a>
						</li>
					`);

					// Insert before help menu
					var $help_menu = $navbar.find('.dropdown').last();
					if ($help_menu.length) {
						$help_menu.before($knowlan_button);
					} else {
						$navbar.append($knowlan_button);
					}
				}
			}
		}, 1000);
	},

	setup_page: function() {
		// Register the Knowlan page
		frappe.standard_pages["knowlan"] = function() {
			var wrapper = frappe.container.add_page("knowlan");
			
			frappe.ui.make_app_page({
				parent: wrapper,
				name: "knowlan",
				title: __("Knowlan Knowledge Base"),
				single_column: true,
			});

			knowlan.page = wrapper.page;
			knowlan.wrapper = wrapper;
			knowlan.make_page();
		};
	},

	show: function() {
		frappe.set_route("knowlan");
	},

	make_page: function() {
		var me = this;
		
		// Set up the page layout
		this.page.set_title(__("Knowlan Knowledge Base"));
		this.page.set_title_sub(__("Visual insights into your Frappe system"));

		// Add main action buttons
		this.page.set_primary_action(__("System Atlas"), function() {
			me.show_system_atlas();
		}, "fa fa-sitemap");

		this.page.add_action_item(__("Learning Classroom"), function() {
			me.show_learning_classroom();
		}, "fa fa-graduation-cap");

		this.page.add_action_item(__("System Insights"), function() {
			me.show_system_insights();
		}, "fa fa-chart-line");

		this.page.add_action_item(__("Audit Dashboard"), function() {
			me.show_audit_dashboard();
		}, "fa fa-history");

		this.page.add_action_item(__("Developer Toolbox"), function() {
			me.show_developer_toolbox();
		}, "fa fa-tools");

		// Create the main content area
		this.make_content();
	},

	make_content: function() {
		var me = this;
		
		// Clear existing content
		this.page.main.empty();
		
		// Create main container
		this.main_container = $(`
			<div class="knowlan-container">
				<div class="knowlan-header">
					<div class="row">
						<div class="col-md-12">
							<h1 class="page-title">
								<i class="fa fa-sitemap"></i>
								${__("Knowlan Knowledge Base")}
							</h1>
							<p class="page-subtitle">
								${__("Visual insights into your Frappe system topology, guided learning paths, and developer tools")}
							</p>
						</div>
					</div>
				</div>
				<div class="knowlan-dashboard">
					<div class="row">
						<div class="col-md-6 col-lg-4">
							<div class="knowlan-card" data-module="system-atlas">
								<div class="card-header">
									<i class="fa fa-sitemap"></i>
									<h3>${__("System Atlas")}</h3>
								</div>
								<div class="card-body">
									<p>${__("Interactive visualization of DocType relationships and system structure")}</p>
									<ul>
										<li>${__("Clustered node-link diagrams")}</li>
										<li>${__("Multiple layout algorithms")}</li>
										<li>${__("Export capabilities")}</li>
									</ul>
								</div>
								<div class="card-footer">
									<button class="btn btn-primary btn-sm" onclick="knowlan.show_system_atlas()">
										${__("Open System Atlas")}
									</button>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="knowlan-card" data-module="learning-classroom">
								<div class="card-header">
									<i class="fa fa-graduation-cap"></i>
									<h3>${__("Learning Classroom")}</h3>
								</div>
								<div class="card-body">
									<p>${__("Structured learning paths through the codebase")}</p>
									<ul>
										<li>${__("Progressive disclosure roadmap")}</li>
										<li>${__("Code snippet viewer")}</li>
										<li>${__("Integrated note-taking")}</li>
									</ul>
								</div>
								<div class="card-footer">
									<button class="btn btn-primary btn-sm" onclick="knowlan.show_learning_classroom()">
										${__("Open Classroom")}
									</button>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="knowlan-card" data-module="system-insights">
								<div class="card-header">
									<i class="fa fa-chart-line"></i>
									<h3>${__("System Insights")}</h3>
								</div>
								<div class="card-body">
									<p>${__("Real-time system behavior analysis")}</p>
									<ul>
										<li>${__("Permission matrix visualization")}</li>
										<li>${__("Workflow state diagrams")}</li>
										<li>${__("Dependency tracking")}</li>
									</ul>
								</div>
								<div class="card-footer">
									<button class="btn btn-primary btn-sm" onclick="knowlan.show_system_insights()">
										${__("Open Insights")}
									</button>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="knowlan-card" data-module="audit-dashboard">
								<div class="card-header">
									<i class="fa fa-history"></i>
									<h3>${__("Audit Dashboard")}</h3>
								</div>
								<div class="card-body">
									<p>${__("Historical analysis and compliance tracking")}</p>
									<ul>
										<li>${__("Document lifecycle timelines")}</li>
										<li>${__("Activity heatmaps")}</li>
										<li>${__("Error log analysis")}</li>
									</ul>
								</div>
								<div class="card-footer">
									<button class="btn btn-primary btn-sm" onclick="knowlan.show_audit_dashboard()">
										${__("Open Dashboard")}
									</button>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="knowlan-card" data-module="developer-toolbox">
								<div class="card-header">
									<i class="fa fa-tools"></i>
									<h3>${__("Developer Toolbox")}</h3>
								</div>
								<div class="card-body">
									<p>${__("Enhanced development and debugging tools")}</p>
									<ul>
										<li>${__("Frappe console with autocomplete")}</li>
										<li>${__("Visual database explorer")}</li>
										<li>${__("Codebase search")}</li>
									</ul>
								</div>
								<div class="card-footer">
									<button class="btn btn-primary btn-sm" onclick="knowlan.show_developer_toolbox()">
										${__("Open Toolbox")}
									</button>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="knowlan-card" data-module="debug-playground">
								<div class="card-header">
									<i class="fa fa-bug"></i>
									<h3>${__("Debug Playground")}</h3>
								</div>
								<div class="card-body">
									<p>${__("Real-time error tracking and debugging workspace")}</p>
									<ul>
										<li>${__("Live error capture and analysis")}</li>
										<li>${__("Network request monitoring")}</li>
										<li>${__("Developer resolution tracking")}</li>
									</ul>
								</div>
								<div class="card-footer">
									<button class="btn btn-primary btn-sm" onclick="knowlan.show_debug_playground()">
										${__("Open Debug Playground")}
									</button>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="knowlan-card" data-module="getting-started">
								<div class="card-header">
									<i class="fa fa-play-circle"></i>
									<h3>${__("Getting Started")}</h3>
								</div>
								<div class="card-body">
									<p>${__("Quick start guide and documentation")}</p>
									<ul>
										<li>${__("Installation guide")}</li>
										<li>${__("Feature overview")}</li>
										<li>${__("Best practices")}</li>
									</ul>
								</div>
								<div class="card-footer">
									<button class="btn btn-primary btn-sm" onclick="knowlan.show_getting_started()">
										${__("Get Started")}
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		`).appendTo(this.page.main);

		// Add click handlers for cards
		this.main_container.find('.knowlan-card').on('click', function() {
			var module = $(this).data('module');
			me.open_module(module);
		});
	},

	open_module: function(module) {
		switch(module) {
			case 'system-atlas':
				this.show_system_atlas();
				break;
			case 'learning-classroom':
				this.show_learning_classroom();
				break;
			case 'system-insights':
				this.show_system_insights();
				break;
			case 'audit-dashboard':
				this.show_audit_dashboard();
				break;
			case 'developer-toolbox':
				this.show_developer_toolbox();
				break;
			case 'debug-playground':
				this.show_debug_playground();
				break;
			case 'getting-started':
				this.show_getting_started();
				break;
		}
	},

	// Module methods
	show_system_atlas: function() {
		this.show_module_content("System Atlas", this.render_system_atlas.bind(this));
	},

	render_system_atlas: function(container) {
		// Load system atlas module
		frappe.require('/assets/knowlan/js/system_atlas.js', function() {
			// Clean up any existing instance
			if (knowlan.atlas && knowlan.atlas.cleanup) {
				knowlan.atlas.cleanup();
			}
			knowlan.atlas.init(container);
		});
	},

	show_learning_classroom: function() {
		this.show_module_content("Classroom", this.render_learning_classroom.bind(this));
	},

	render_learning_classroom: function(container) {
		// Load learning classroom module with retry mechanism
		var me = this;
		var max_retries = 3;
		var retry_count = 0;

		function attempt_load() {
			frappe.require('/assets/knowlan/js/learning_classroom.js', function() {
				// Give a small delay to ensure everything is initialized
				setTimeout(function() {
					if (typeof knowlan !== 'undefined' && knowlan.learning_classroom && knowlan.learning_classroom.init) {
						knowlan.learning_classroom.init(container);
					} else {
						retry_count++;
						if (retry_count < max_retries) {
							console.warn('Classroom module not ready, retrying... (' + retry_count + '/' + max_retries + ')');
							setTimeout(attempt_load, 500);
						} else {
							console.error('Classroom module failed to load after ' + max_retries + ' attempts');
							container.html('<div class="alert alert-danger">' +
								'<h5>Error loading Classroom</h5>' +
								'<p>The Classroom module failed to load properly. Please refresh the page.</p>' +
								'<button class="btn btn-primary" onclick="window.location.reload()">Refresh Page</button>' +
								'</div>');
						}
					}
				}, 100);
			});
		}

		attempt_load();
	},

	show_system_insights: function() {
		this.show_module_content("System Insights", this.render_system_insights.bind(this));
	},

	render_system_insights: function(container) {
		// Use lazy loader to ensure D3.js is loaded first
		if (knowlan.lazy_loader) {
			knowlan.lazy_loader.load_module_async('insights_dashboard').then(function() {
				knowlan.insights_dashboard.init(container);
			}).catch(function(error) {
				console.error('Failed to load insights dashboard:', error);
				// Fallback to direct loading
				frappe.require('/assets/knowlan/js/insights_dashboard.js', function() {
					knowlan.insights_dashboard.init(container);
				});
			});
		} else {
			// Fallback if lazy loader not available
			frappe.require('/assets/knowlan/js/insights_dashboard.js', function() {
				knowlan.insights_dashboard.init(container);
			});
		}
	},

	show_audit_dashboard: function() {
		this.show_module_content("Audit Dashboard", this.render_audit_dashboard.bind(this));
	},

	render_audit_dashboard: function(container) {
		// Use lazy loader to ensure vis.js is loaded first
		if (knowlan.lazy_loader) {
			knowlan.lazy_loader.load_module_async('audit_dashboard').then(function() {
				knowlan.audit_dashboard.init(container);
			}).catch(function(error) {
				console.error('Failed to load audit dashboard:', error);
				// Fallback to direct loading
				frappe.require('/assets/knowlan/js/audit_dashboard.js', function() {
					knowlan.audit_dashboard.init(container);
				});
			});
		} else {
			// Fallback if lazy loader not available
			frappe.require('/assets/knowlan/js/audit_dashboard.js', function() {
				knowlan.audit_dashboard.init(container);
			});
		}
	},

	show_developer_toolbox: function() {
		this.show_module_content("Developer Toolbox", this.render_developer_toolbox.bind(this));
	},

	render_developer_toolbox: function(container) {
		// Load developer toolbox module
		frappe.require('/assets/knowlan/js/developer_toolbox.js', function() {
			knowlan.developer_toolbox.init(container);
		});
	},

	show_debug_playground: function() {
		this.show_module_content("Debug Playground", this.render_debug_playground.bind(this));
	},

	render_debug_playground: function(container) {
		console.log('Loading debug playground module...');
		// Load debug playground module
		frappe.require('/assets/knowlan/js/debug_playground.js', function() {
			console.log('Debug playground module loaded, initializing...');
			if (knowlan.debug_playground && knowlan.debug_playground.init) {
				knowlan.debug_playground.init(container);
			} else {
				console.error('Debug playground module not found or init function missing');
			}
		});
	},

	show_getting_started: function() {
		frappe.msgprint(__("Getting Started guide coming soon!"));
	},

	show_module_content: function(title, render_callback) {
		// Clear existing content
		this.page.main.empty();

		// Create module container
		var module_container = $(`
			<div class="knowlan-module-container">
				<div class="knowlan-module-header">
					<div class="row">
						<div class="col-md-8">
							<h2 class="module-title">
								<i class="fa fa-arrow-left back-button" style="cursor: pointer; margin-right: 15px;"></i>
								${title}
							</h2>
						</div>
						<div class="col-md-4 text-right">
							<button class="btn btn-default btn-sm fullscreen-toggle">
								<i class="fa fa-expand"></i> Fullscreen
							</button>
						</div>
					</div>
				</div>
				<div class="knowlan-module-content">
					<!-- Module content will be rendered here -->
				</div>
			</div>
		`).appendTo(this.page.main);

		// Setup event handlers
		var me = this;
		module_container.find('.back-button').on('click', function() {
			me.make_content();
		});

		module_container.find('.fullscreen-toggle').on('click', function() {
			var icon = $(this).find('i');
			if (icon.hasClass('fa-expand')) {
				module_container.addClass('fullscreen');
				icon.removeClass('fa-expand').addClass('fa-compress');
				$(this).find('span').text('Exit Fullscreen');
			} else {
				module_container.removeClass('fullscreen');
				icon.removeClass('fa-compress').addClass('fa-expand');
				$(this).find('span').text('Fullscreen');
			}
		});

		// Render module content
		var content_container = module_container.find('.knowlan-module-content');
		render_callback(content_container);
	},

	setup_accessibility: function() {
		// Add skip to content link
		if (!$('.skip-to-content').length) {
			$('body').prepend(`
				<a href="#main-content" class="skip-to-content">
					${__("Skip to main content")}
				</a>
			`);
		}

		// Add ARIA landmarks
		$(document).on('page:knowlan', function() {
			// Add main landmark
			$('.knowlan-container').attr({
				'role': 'main',
				'id': 'main-content',
				'aria-label': __('Knowlan Knowledge Base Main Content')
			});

			// Add navigation landmarks
			$('.knowlan-dashboard').attr({
				'role': 'navigation',
				'aria-label': __('Module Navigation')
			});

			// Improve card accessibility
			$('.knowlan-card').each(function(index) {
				var $card = $(this);
				var module = $card.data('module');
				var title = $card.find('h3').text();

				$card.attr({
					'role': 'button',
					'tabindex': '0',
					'aria-label': __('Open {0} module', [title]),
					'aria-describedby': 'card-desc-' + index
				});

				// Add description for screen readers
				var description = $card.find('.card-body p').text();
				$card.find('.card-body').append(`
					<div id="card-desc-${index}" class="sr-only">
						${description}
					</div>
				`);
			});

			// Add keyboard navigation for cards
			$('.knowlan-card').on('keydown', function(e) {
				if (e.key === 'Enter' || e.key === ' ') {
					e.preventDefault();
					$(this).click();
				}
			});
		});

		// Improve focus management
		this.setup_focus_management();
	},

	setup_focus_management: function() {
		var me = this;

		// Focus management for module navigation
		$(document).on('click', '.back-button', function() {
			// Return focus to the card that was clicked
			setTimeout(function() {
				$('.knowlan-card').first().focus();
			}, 100);
		});

		// Trap focus in modals and dropdowns
		$(document).on('shown.bs.modal', '.modal', function() {
			var $modal = $(this);
			var $focusableElements = $modal.find('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
			var $firstElement = $focusableElements.first();
			var $lastElement = $focusableElements.last();

			$firstElement.focus();

			$modal.on('keydown', function(e) {
				if (e.key === 'Tab') {
					if (e.shiftKey) {
						if (document.activeElement === $firstElement[0]) {
							e.preventDefault();
							$lastElement.focus();
						}
					} else {
						if (document.activeElement === $lastElement[0]) {
							e.preventDefault();
							$firstElement.focus();
						}
					}
				}
			});
		});
	},

	setup_responsive_handlers: function() {
		var me = this;

		// Handle window resize
		$(window).on('resize', frappe.utils.debounce(function() {
			me.handle_responsive_layout();
		}, 250));

		// Handle orientation change
		$(window).on('orientationchange', function() {
			setTimeout(function() {
				me.handle_responsive_layout();
			}, 100);
		});

		// Initial layout setup
		this.handle_responsive_layout();
	},

	handle_responsive_layout: function() {
		var windowWidth = $(window).width();
		var $container = $('.knowlan-container');

		// Mobile layout adjustments
		if (windowWidth < 768) {
			$container.addClass('mobile-layout');

			// Stack cards vertically
			$('.knowlan-dashboard .row').addClass('mobile-stack');

			// Adjust module headers
			$('.knowlan-module-header .row').addClass('mobile-header');

			// Hide less important elements
			$('.page-subtitle').addClass('d-none d-md-block');

		} else {
			$container.removeClass('mobile-layout');
			$('.knowlan-dashboard .row').removeClass('mobile-stack');
			$('.knowlan-module-header .row').removeClass('mobile-header');
			$('.page-subtitle').removeClass('d-none d-md-block');
		}

		// Tablet layout adjustments
		if (windowWidth >= 768 && windowWidth < 992) {
			$container.addClass('tablet-layout');
		} else {
			$container.removeClass('tablet-layout');
		}

		// Desktop layout adjustments
		if (windowWidth >= 992) {
			$container.addClass('desktop-layout');
		} else {
			$container.removeClass('desktop-layout');
		}

		// Trigger resize event for modules
		$(document).trigger('knowlan:layout_changed', {
			width: windowWidth,
			isMobile: windowWidth < 768,
			isTablet: windowWidth >= 768 && windowWidth < 992,
			isDesktop: windowWidth >= 992
		});
	},

	// Enhanced error handling
	handle_error: function(error, context) {
		console.error('Knowlan Error:', error, 'Context:', context);

		// Show user-friendly error message
		frappe.show_alert({
			message: __('An error occurred in Knowlan. Please try again or contact support.'),
			indicator: 'red'
		});

		// Log error for debugging
		if (frappe.boot.developer_mode) {
			frappe.msgprint({
				title: __('Knowlan Debug Info'),
				message: `
					<strong>Error:</strong> ${error.message || error}<br>
					<strong>Context:</strong> ${context}<br>
					<strong>Stack:</strong> <pre>${error.stack || 'No stack trace'}</pre>
				`,
				indicator: 'red'
			});
		}
	},

	// Performance monitoring
	monitor_performance: function() {
		if (window.performance && window.performance.mark) {
			// Mark performance points
			performance.mark('knowlan-init-start');

			// Monitor module load times
			$(document).on('knowlan:module_loaded', function(e, data) {
				performance.mark(`knowlan-${data.module}-loaded`);
				performance.measure(
					`knowlan-${data.module}-load-time`,
					'knowlan-init-start',
					`knowlan-${data.module}-loaded`
				);
			});
		}
	}
});
