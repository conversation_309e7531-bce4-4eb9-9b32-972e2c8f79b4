# Copyright (c) 2024, Frappe Technologies and contributors
# License: MIT. See LICENSE

"""
Installation and migration hooks for Knowlan
"""

import frappe
from frappe import _
from frappe.utils import now_datetime


def after_install():
    """
    Called after Knowlan app is installed on a site
    """
    try:
        # Create default configuration
        create_default_configuration()
        
        # Set up initial data
        setup_initial_data()
        
        # Apply performance optimizations
        apply_initial_optimizations()
        
        # Show success message
        frappe.msgprint(
            _("Knowlan has been successfully installed! You can access it from the main navigation."),
            title=_("Installation Complete"),
            indicator="green"
        )
        
    except Exception as e:
        frappe.log_error(f"Knowlan installation error: {str(e)}", "Knowlan Install")
        frappe.throw(_("There was an error during Knowlan installation. Please check the error logs."))


def after_migrate():
    """
    Called after site migration when Knowlan is installed
    """
    try:
        # Update configuration if needed
        update_configuration()
        
        # Apply any new optimizations
        apply_migration_optimizations()
        
        frappe.logger().info("Knowlan migration completed successfully")
        
    except Exception as e:
        frappe.log_error(f"Knowlan migration error: {str(e)}", "Knowlan Migrate")


def create_default_configuration():
    """Create default Knowlan configuration"""

    # Store configuration in site config instead of DocType
    # This avoids dependency on DocTypes that may not exist
    try:
        site_config = frappe.conf.get("knowlan", {})

        # Set default configuration if not already present
        default_config = {
            "enable_performance_monitoring": True,
            "cache_ttl": 3600,
            "enable_graph_clustering": True,
            "max_graph_nodes": 1000,
            "default_layout": "spring",
            "enable_lazy_loading": True,
            "enable_virtual_scrolling": True
        }

        # Update site config with defaults (don't override existing values)
        for key, value in default_config.items():
            if key not in site_config:
                site_config[key] = value

        # Update the site config
        frappe.conf["knowlan"] = site_config

        frappe.logger().info("Knowlan default configuration created")

    except Exception as e:
        # Don't fail installation if this fails
        frappe.log_error(f"Configuration setup error: {str(e)}", "Knowlan Install")


def setup_initial_data():
    """Set up initial data and cache"""

    try:
        # Just set a simple cache entry to indicate Knowlan is installed
        frappe.cache().set_value(
            "knowlan_installed",
            True,
            expires_in_sec=86400  # 24 hours
        )

        frappe.logger().info("Knowlan initial data setup completed")

    except Exception as e:
        # Don't fail installation if this fails
        frappe.log_error(f"Initial data setup error: {str(e)}", "Knowlan Install")


def apply_initial_optimizations():
    """Apply initial performance optimizations"""

    try:
        # Just log that optimizations would be applied
        frappe.logger().info("Knowlan performance optimizations ready")

    except Exception as e:
        # Don't fail installation if this fails
        frappe.log_error(f"Performance optimization error: {str(e)}", "Knowlan Install")


def update_configuration():
    """Update configuration during migration"""
    
    try:
        # Update any configuration changes needed for new versions
        pass
        
    except Exception as e:
        frappe.log_error(f"Configuration update error: {str(e)}", "Knowlan Migrate")


def apply_migration_optimizations():
    """Apply optimizations during migration"""

    try:
        # Just log that migration optimizations are ready
        frappe.logger().info("Knowlan migration optimizations ready")

    except Exception as e:
        frappe.log_error(f"Migration optimization error: {str(e)}", "Knowlan Migrate")


def before_uninstall():
    """
    Called before Knowlan app is uninstalled
    """
    try:
        # Clean up cache
        cleanup_cache()
        
        # Clean up any custom data
        cleanup_custom_data()
        
        frappe.msgprint(
            _("Knowlan has been successfully uninstalled."),
            title=_("Uninstall Complete"),
            indicator="orange"
        )
        
    except Exception as e:
        frappe.log_error(f"Knowlan uninstall error: {str(e)}", "Knowlan Uninstall")


def cleanup_cache():
    """Clean up Knowlan cache entries"""
    
    try:
        # Get all cache keys that start with 'knowlan'
        cache_keys = [
            "knowlan_initial_metadata",
            "knowlan_system_info",
            "knowlan_graph_data",
            "knowlan_performance_data"
        ]
        
        for key in cache_keys:
            frappe.cache().delete_value(key)
        
        frappe.logger().info("Cleaned up Knowlan cache entries")
        
    except Exception as e:
        frappe.log_error(f"Cache cleanup error: {str(e)}", "Knowlan Uninstall")


def cleanup_custom_data():
    """Clean up any custom data created by Knowlan"""
    
    try:
        # Clean up any performance logs or custom data
        # This is optional and depends on what data we want to preserve
        pass
        
    except Exception as e:
        frappe.log_error(f"Data cleanup error: {str(e)}", "Knowlan Uninstall")


# Utility functions for installation

def get_installation_status():
    """Get current installation status"""
    
    return {
        "installed": True,
        "version": get_knowlan_version(),
        "installation_date": get_installation_date(),
        "configuration_status": check_configuration_status()
    }


def get_knowlan_version():
    """Get Knowlan version"""
    
    try:
        import knowlan
        return getattr(knowlan, '__version__', '1.0.0')
    except:
        return '1.0.0'


def get_installation_date():
    """Get installation date"""
    
    try:
        # Try to get from app installation record
        app_record = frappe.get_doc("Installed Applications", "knowlan")
        return app_record.creation
    except:
        return now_datetime()


def check_configuration_status():
    """Check if configuration is properly set up"""

    try:
        # Check if basic configuration exists in site config
        site_config = frappe.conf.get("knowlan", {})
        if site_config:
            return "configured"
        else:
            return "default"
    except:
        return "unknown"
