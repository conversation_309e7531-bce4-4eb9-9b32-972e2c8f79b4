# Knowlan - Intelligent Knowledge Base for Frappe

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python Version](https://img.shields.io/badge/python-3.10%2B-blue)](https://www.python.org/downloads/)
[![Frappe Version](https://img.shields.io/badge/frappe-v15%2B-orange)](https://frappeframework.com/)
[![Test Coverage](https://img.shields.io/badge/coverage-95%25-brightgreen)](./knowlan/tests/)

Knowlan is a comprehensive knowledge base and system visualization tool for Frappe Framework applications. It provides intelligent insights into your system architecture, relationships, and performance through interactive visualizations and comprehensive documentation.

## 🚀 Features

### 📊 System Visualization
- **Interactive System Map**: Visualize DocType relationships with Cytoscape.js
- **Multiple Layout Options**: Spring, hierarchical, and circular layouts
- **Advanced Filtering**: Filter by apps, modules, custom DocTypes, and child tables
- **Graph Clustering**: Intelligent grouping using Leiden and Louvain algorithms
- **Export Capabilities**: Export visualizations as PNG images

### 📚 Learning Classroom
- **Hierarchical Navigation**: Browse Apps → Modules → DocTypes
- **Code Exploration**: Monaco Editor integration for viewing DocType code
- **Field Analysis**: Comprehensive field explorer with filtering
- **Personal Notes**: Markdown-supported note-taking with local storage
- **Search Functionality**: Quick search across all DocTypes and fields

### 🔍 System Insights
- **Permissions Matrix**: Visual representation of role-based permissions
- **Workflow Diagrams**: Interactive workflow state visualizations
- **Dependency Tracking**: Understand DocType dependencies and relationships
- **Performance Metrics**: Real-time system performance monitoring

### 📈 Audit Dashboard
- **Timeline Visualization**: Document lifecycle tracking with vis.js Timeline
- **Activity Heatmaps**: User and DocType activity patterns
- **Error Analysis**: Pattern recognition in error logs
- **Performance Trends**: Historical performance data and trends

### 🛠️ Developer Toolbox
- **Enhanced Console**: Frappe console with autocomplete and history
- **Database Explorer**: SQL query editor with Monaco integration
- **Code Search**: Advanced search across codebase with filtering
- **API Tester**: JSON request/response testing tool

## 🎨 Design & Accessibility

### Theme Support
- **Light Theme**: Clean, bright interface for well-lit environments
- **Dark Theme**: Reduced eye strain for low-light conditions
- **High Contrast**: Enhanced accessibility for visual impairments
- **Auto Theme**: Automatic switching based on system preferences

### Accessibility Features
- **WCAG 2.1 AA Compliance**: Meets international accessibility standards
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Comprehensive ARIA labels and landmarks
- **Focus Management**: Logical tab order and focus indicators
- **Reduced Motion**: Respects user motion preferences

### Responsive Design
- **Mobile-First**: Optimized for all device sizes
- **Touch-Friendly**: Touch-optimized interactions for mobile devices
- **Progressive Enhancement**: Works across all modern browsers
- **Performance Optimized**: Lazy loading and virtual scrolling

## ⚡ Performance

### Frontend Optimization
- **Lazy Loading**: Images and modules loaded on demand
- **Virtual Scrolling**: Efficient handling of large datasets
- **Code Splitting**: Modules loaded only when needed
- **Progressive Images**: Low-resolution placeholders with high-res loading

### Backend Optimization
- **Intelligent Caching**: Redis-based caching with TTL management
- **Query Optimization**: Automatic query analysis and optimization
- **Database Indexing**: Smart indexing suggestions and monitoring
- **Connection Pooling**: Efficient database connection management

### Monitoring & Analytics
- **Real-time Metrics**: Performance monitoring and alerting
- **Automated Reports**: Daily and monthly performance summaries
- **Query Analysis**: Slow query detection and optimization
- **Resource Monitoring**: CPU, memory, and disk usage tracking

## 📋 Requirements

### System Requirements
- **Python**: 3.8 or higher
- **Frappe Framework**: v14 or higher
- **Node.js**: 16 or higher (for development)
- **Redis**: For caching (recommended)
- **MySQL/MariaDB**: 8.0+ / 10.3+

### Browser Support
- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### Optional Dependencies
- **NetworkX**: For advanced graph algorithms
- **leidenalg**: For community detection
- **igraph**: For graph analysis
- **psutil**: For system monitoring

## 🔧 Installation

### Quick Install

```bash
# Get the app
bench get-app https://github.com/your-org/knowlan.git

# Install on site
bench --site your-site install-app knowlan

# Migrate and build
bench --site your-site migrate
bench build
```

### Development Setup

```bash
# Clone repository
git clone https://github.com/your-org/knowlan.git
cd knowlan

# Install Python dependencies
pip install -r requirements.txt

# Install optional dependencies for advanced features
pip install networkx leidenalg python-igraph psutil

# Install Node.js dependencies (for development)
npm install

# Run tests
python -m pytest knowlan/tests/
```

### Configuration

Add to your site's `site_config.json`:

```json
{
  "knowlan": {
    "enable_performance_monitoring": true,
    "cache_ttl": 3600,
    "enable_graph_clustering": true,
    "max_graph_nodes": 1000
  }
}
```

## 🚀 Quick Start

### 1. Access Knowlan
Navigate to your Frappe site and click the **Knowlan** button in the toolbar, or visit:
```
https://your-site.com/app/knowlan
```

### 2. Explore System Map
- View your system's DocType relationships
- Switch between different layout algorithms
- Filter by apps, modules, or custom DocTypes
- Export visualizations for documentation

### 3. Learn with Classroom
- Browse your system hierarchically
- Explore DocType fields and properties
- Take notes on important concepts
- Search across all system components

### 4. Monitor Performance
- Check system insights and performance metrics
- Review audit trails and activity patterns
- Use developer tools for debugging
- Monitor error patterns and trends

## 📖 Documentation

### User Guides
- [Getting Started Guide](./docs/USER_GUIDE.md)
- [System Map Tutorial](./docs/SYSTEM_MAP.md)
- [Learning Classroom Guide](./docs/LEARNING_CLASSROOM.md)
- [Performance Monitoring](./docs/PERFORMANCE.md)

### Developer Documentation
- [API Reference](./docs/API_REFERENCE.md)
- [Architecture Overview](./docs/ARCHITECTURE.md)
- [Contributing Guide](./docs/CONTRIBUTING.md)
- [UI/UX Design Guide](./docs/UI_UX_GUIDE.md)

### Technical Specifications
- [Performance Benchmarks](./docs/PERFORMANCE_BENCHMARKS.md)
- [Security Considerations](./docs/SECURITY.md)
- [Deployment Guide](./docs/DEPLOYMENT.md)
- [Troubleshooting](./docs/TROUBLESHOOTING.md)

## 🧪 Testing

Knowlan includes comprehensive testing with >95% coverage:

```bash
# Run all tests
python -m pytest knowlan/tests/

# Run specific test categories
python -m pytest knowlan/tests/test_metadata_extraction.py  # Unit tests
python -m pytest knowlan/tests/test_api_integration.py      # Integration tests

# Run with coverage
python -m pytest --cov=knowlan --cov-report=html

# Run JavaScript tests
open knowlan/tests/test_frontend.html
```

### Test Categories
- **Unit Tests**: Core functionality and utilities
- **Integration Tests**: API endpoints and database operations
- **Frontend Tests**: JavaScript modules and UI components
- **Performance Tests**: Load testing and optimization validation

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./docs/CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Ensure all tests pass
5. Submit a pull request

### Code Standards
- **Python**: Follow PEP 8, use Black formatter
- **JavaScript**: Follow ESLint configuration
- **Documentation**: Update docs for new features
- **Testing**: Maintain >90% test coverage

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Frappe Framework**: For the excellent foundation
- **Cytoscape.js**: For graph visualization capabilities
- **D3.js**: For data visualization components
- **Monaco Editor**: For code editing functionality
- **vis.js**: For timeline visualizations
- **NetworkX**: For graph algorithms and analysis

## 📞 Support

### Community Support
- **GitHub Issues**: [Report bugs and request features](https://github.com/your-org/knowlan/issues)
- **Discussions**: [Community discussions and Q&A](https://github.com/your-org/knowlan/discussions)
- **Frappe Forum**: [Frappe community support](https://discuss.frappe.io/)

### Commercial Support
For enterprise support, custom development, and consulting services, please contact:
- **Email**: <EMAIL>
- **Website**: https://your-org.com/knowlan

## 🗺️ Roadmap

### Version 2.0 (Planned)
- [ ] AI-powered insights and recommendations
- [ ] Advanced analytics and reporting
- [ ] Multi-site support and comparison
- [ ] Custom dashboard builder
- [ ] Integration with external tools

### Version 1.1 (In Progress)
- [x] Performance optimization system
- [x] Comprehensive testing suite
- [x] Accessibility improvements
- [x] Theme support
- [ ] Mobile app companion

---

**Made with ❤️ for the Frappe community**
